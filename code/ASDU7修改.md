# ASDU7修改设计方案

## 需求分析

根据福建103规约的新增需求，需要在"全站总召"时增加"装置当前运行定值区"作为特殊遥信上送，具体要求：

1. **功能类型**: 250 (0xFA)  
2. **信息序号**: 装置地址（n_outaddr103）
3. **DPI值**: 当前定值区号
4. **上送时机**: 响应总召时上送

## 现状分析

### 当前ASDU7处理流程
在 `TNXEcProAsdu7GWS::DirectResFromLocal()` 中：

```cpp
// 1. 通信状态
int nRet = MakeAsdu7InfoList(lInfoObj); 
pAsdu42->FormatAsdu42Body(Asdu7Addr,lInfoObj,nScn,lResult);

// 2. 运行状态  
nRet = MakeAsdu7InfoList_runstatus(lInfoObj);
pAsdu42->FormatAsdu42Body(Asdu7Addr,lInfoObj,nScn,lResult);

// 3. 总召结束
MakeAsdu8Body(Asdu7Addr,nScn,lResult);
```

### 现有代码结构
- `MakeAsdu7InfoList()`: 继承自基类，生成通信状态（FUN=255）
- `MakeAsdu7InfoList_runstatus()`: GWS特有，生成运行状态（FUN=251）  
- 都使用 `GetAllIedStatus()` 获取IED列表
- 都使用 `n_outaddr103` 作为信息序号

## 存在的问题

当前缺少"定值区遥信"的生成和上送逻辑。

## 解决方案

### 具体实现

#### 1. 头文件修改 (NXEcProAsdu7_GWS.h)

在 `protected` 区域添加新方法声明：

```cpp
/**
* @brief		生成已接入设备定值区信息列表:fun=250,103地址(inf),dpi=当前定值区号			
* @param[out]   OUT INFO_OBJ_LIST& lInfoObj:返回信息列表
* @return		0-执行成功；其他-执行失败
**/
int MakeAsdu7InfoList_settinggroup(OUT INFO_OBJ_LIST& lInfoObj);
```

#### 2. 源文件修改 (NXEcProAsdu7_GWS.cpp)

**2.1 修改主流程**

在 `DirectResFromLocal()` 方法中，在运行状态和总召结束之间插入定值区处理：

```cpp
// 现有的运行状态处理
lInfoObj.clear();
nRet = MakeAsdu7InfoList_runstatus(lInfoObj); //运行状态
if (nRet < 0)
{
    RcdErrLogWithParentClass("生成设备通信运行状态信息时出错.","TNXEcProAsdu7GWS");
}
else
{
    pAsdu42->FormatAsdu42Body(Asdu7Addr,lInfoObj,nScn,lResult);
}

// 【新增】定值区状态处理
lInfoObj.clear();
nRet = MakeAsdu7InfoList_settinggroup(lInfoObj); //定值区状态
if (nRet < 0)
{
    RcdErrLogWithParentClass("生成设备定值区状态信息时出错.","TNXEcProAsdu7GWS");
}
else
{
    pAsdu42->FormatAsdu42Body(Asdu7Addr,lInfoObj,nScn,lResult);
}

// 现有的总召结束
MakeAsdu8Body(Asdu7Addr,nScn,lResult);
```

**2.2 实现新方法**

参考 `MakeAsdu7InfoList_runstatus()` 的实现模式：

```cpp
int TNXEcProAsdu7GWS::MakeAsdu7InfoList_settinggroup(OUT INFO_OBJ_LIST& lInfoObj)
{
    //获取当前接入设备列表信息.
    LIST_IED ListIed;
    ASDU_INFO_OBJ InfoObj;

    bool bRet = m_pModelSeek->GetAllIedStatus(ListIed);
    if (!bRet)
    {
        RcdErrLogWithParentClass("查找全站设备信息时,函数GetAllIedStatus出错.","TNXEcProAsdu7GWS");
        return -1;
    }

    if(ListIed.size()==0) return -1;//没有找到设备,也按失败处理.

    LIST_IED::iterator ite=ListIed.begin();
    while(ite != ListIed.end() )
    {
        if( ite->n_outaddr103 <= 0 )        // 对上通信无效的过滤
        {
            ++ite;
            continue;
        }
        
        InfoObj.nFun = 250;  // 0xFA - 定值区功能类型
        InfoObj.nInf = ite->n_outaddr103;   // 103地址作为信息序号
        
        // 获取当前定值区号
        // 参考 TNXEcProAsdu21::__GetCurrentZoneValue() 的实现
        SGZONE_TB tbSgZone;
        int nCurrentZone = 1; // 默认定值区1
        
        // 通过ModelSeek获取IED的LD配置，然后查找当前定值区
        const LD_TB * pLdTb = m_pModelSeek->GetIedLdModelCfg(ite->n_obj_id, 0); // CPU=0为默认
        if (pLdTb != NULL)
        {
            LIST_SGZONE::const_iterator iteZone = pLdTb->v_sgzone.begin();
            while(iteZone != pLdTb->v_sgzone.end())
            {
                if (iteZone->n_psrtype == 1) // psrtype=1表示当前定值区
                {
                    nCurrentZone = iteZone->n_curvalue;
                    break;
                }
                iteZone++;
            }
        }
        
        InfoObj.nDpi = nCurrentZone;
        
        lInfoObj.push_back(InfoObj);
        ++ite;
    }

    ListIed.clear();
    return 0;
}
```