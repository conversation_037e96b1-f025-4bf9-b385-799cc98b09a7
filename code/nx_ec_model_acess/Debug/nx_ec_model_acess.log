﻿生成启动时间为 2025/7/29 16:40:33。
     1>项目“D:\code\nx_ec_model_acess\nx_ec_model_acess.vcxproj”在节点 3 上(build 个目标)。
     1>C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\Microsoft.CppBuild.targets(299,5): warning MSB8004: Output 目录未以斜杠结尾。此生成实例将添加斜杠，因为必须有这个斜杠才能正确计算 Output 目录。
     1>InitializeBuildStatus:
         正在创建“Debug\nx_ec_model_access.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         D:\vs2010\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDLL /D _MBCS /D __PLATFORM_MS_WIN__ /D _CRT_SECURE_NO_WARNINGS /D NX_EC_MODEL_ACCESS_EXPORT /D _USE_32BIT_TIME_T /Gm /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Fo"Debug\\" /Fd"Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ..\..\..\..\nx_common\CsgLogRecord.cpp ..\..\..\..\nx_common\CsgLogRecordMngr.cpp ..\..\..\..\nx_common\load_nxdbm_lib.cpp ..\ec_common\NXECObject.cpp ec_model_modify_note.cpp GetEcModelIns.cpp NXEcModelMgr.cpp NXEcSSModelSeek.cpp
         NXEcSSModelSeek.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcModelMgr.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         GetEcModelIns.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         ec_model_modify_note.cpp
     1>d:\code\nx_ec_model_acess\ec_model_modify_note.cpp(27): warning C4138: 在注释外找到“*/”
     1>d:\code\nx_ec_model_acess\ec_model_modify_note.cpp(27): error C2059: 语法错误:“/”
         NXECObject.cpp
     1>d:\code\ec_common\nxecobject.h(11): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         load_nxdbm_lib.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\nx_common\load_nxdbm_lib.cpp”: No such file or directory
         CsgLogRecordMngr.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\nx_common\CsgLogRecordMngr.cpp”: No such file or directory
         CsgLogRecord.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\nx_common\CsgLogRecord.cpp”: No such file or directory
         正在生成代码...
     1>已完成生成项目“D:\code\nx_ec_model_acess\nx_ec_model_acess.vcxproj”(build 个目标)的操作 - 失败。

生成失败。

已用时间 00:00:01.47
