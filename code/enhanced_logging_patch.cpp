// 增强日志版本的__RecordCtrlLogToDb函数修改建议
// 在现有函数基础上添加更详细的调试日志

void CNXEcSrvProOperation::__RecordCtrlLogToDb(NX_COMMON_MSG_LIST * pNxCmdList)
{
	char cErr[250] = "";
	static int debug_call_count = 0;
	int current_call_id = ++debug_call_count;
	
	// 增强的函数入口日志
	sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 函数开始执行，线程ID=%lu", 
		current_call_id, (unsigned long)pthread_self());
	RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
	
	// 增强的this指针检查
	sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): this指针=0x%p", current_call_id, this);
	RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
	
	if (NULL == this)
	{
		sprintf(cErr,"[CALL-%d] CRITICAL: this指针为空!", current_call_id);
		RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
		return;
	}
	
	if (NULL == pNxCmdList)
	{
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 输入参数pNxCmdList为空指针", current_call_id);
		RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
		return;
	}
	
	// 增强的容器状态检查
	sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): pNxCmdList=0x%p, size=%zu", 
		current_call_id, pNxCmdList, pNxCmdList->size());
	RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
	
	if (pNxCmdList->empty())
	{
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 命令队列为空，无需记录日志", current_call_id);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		return;
	}
	
	// 增强的成员指针检查
	sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): m_pModelSeekIns=0x%p, m_pProParam=0x%p", 
		current_call_id, m_pModelSeekIns, m_pProParam);
	RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
	
	if (NULL == m_pModelSeekIns)
	{
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): m_pModelSeekIns为空指针", current_call_id);
		RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
		return;
	}
	
	if (NULL == m_pProParam)
	{
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): m_pProParam为空指针", current_call_id);
		RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
		return;
	}
	
	// 增强的迭代器初始化日志
	sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 开始初始化迭代器", current_call_id);
	RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
	
	NX_COMMON_MSG_LIST::iterator iteCmd;
	try {
		iteCmd = pNxCmdList->begin();
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 迭代器初始化成功，地址=0x%p", 
			current_call_id, &iteCmd);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
	} catch(...) {
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 迭代器初始化时发生异常", current_call_id);
		RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
		return;
	}
	
	int nCmdIndex = 0;
	while( iteCmd != pNxCmdList->end())
	{
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 开始处理第%d个命令，迭代器状态检查", 
			current_call_id, nCmdIndex);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		
		// 增强的迭代器有效性检查
		try {
			// 先检查迭代器是否有效
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 准备访问迭代器内容，索引=%d", 
				current_call_id, nCmdIndex);
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
			
			NX_COMMON_MESSAGE TmpNxCmd = *iteCmd;
			
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 成功获取命令数据，obj_id=%d, msg_type=%d, list_subfields.size()=%zu", 
				current_call_id, TmpNxCmd.n_obj_id, TmpNxCmd.n_msg_type, TmpNxCmd.list_subfields.size());
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		}
		catch(...) {
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 访问第%d个命令数据时发生异常", 
				current_call_id, nCmdIndex);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			++iteCmd;
			++nCmdIndex;
			continue;
		}
		
		NX_COMMON_MESSAGE TmpNxCmd = *iteCmd;

		CTRL_INFO_RECORD CtrlInfoSet;
		
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 开始获取IED配置信息，IED_ID=%d", 
			current_call_id, TmpNxCmd.n_obj_id);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		
		// 增强的IED配置获取
		const EC_IED * pEcIedTb = NULL;
		try {
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 调用GetIedCfgByID，m_pModelSeekIns=0x%p", 
				current_call_id, m_pModelSeekIns);
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
			
			pEcIedTb = m_pModelSeekIns->GetIedCfgByID(TmpNxCmd.n_obj_id);
			
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): GetIedCfgByID返回，pEcIedTb=0x%p", 
				current_call_id, pEcIedTb);
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		} catch(...) {
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 调用GetIedCfgByID时发生异常", current_call_id);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			++iteCmd;
			++nCmdIndex;
			continue;
		}
		
		if ( NULL == pEcIedTb )
		{
			sprintf(cErr,"[CALL-%d] 获取IED=%d的配置信息失败.", current_call_id, TmpNxCmd.n_obj_id);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			++iteCmd;
			++nCmdIndex;
			continue;
		}
		
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 成功获取IED配置信息", current_call_id);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		
		// 增强的IED配置指针检查
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 检查IED配置指针，pPrimDevCfg=0x%p, pIed=0x%p", 
			current_call_id, pEcIedTb->pPrimDevCfg, pEcIedTb->pIed);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		
		if (NULL == pEcIedTb->pPrimDevCfg)
		{
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): IED=%d的主设备配置指针为空", 
				current_call_id, TmpNxCmd.n_obj_id);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			++iteCmd;
			++nCmdIndex;
			continue;
		}
		
		if (NULL == pEcIedTb->pIed)
		{
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): IED=%d的设备指针为空", 
				current_call_id, TmpNxCmd.n_obj_id);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			++iteCmd;
			++nCmdIndex;
			continue;
		}
		
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 开始获取子站配置信息", current_call_id);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		
		const SUBSTATION_TB * pSubStnTB = NULL;
		try {
			pSubStnTB = m_pModelSeekIns->GetSubStationBasicCfg();
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): GetSubStationBasicCfg返回，pSubStnTB=0x%p", 
				current_call_id, pSubStnTB);
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		} catch(...) {
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 调用GetSubStationBasicCfg时发生异常", current_call_id);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			++iteCmd;
			++nCmdIndex;
			continue;
		}
		
		if ( NULL == pSubStnTB )
		{
			sprintf(cErr,"[CALL-%d] 获取当前子站配置信息失败.", current_call_id);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			++iteCmd;
			++nCmdIndex;
			continue;
		}
		
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 成功获取子站配置信息", current_call_id);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");

		// 开始填充控制信息记录结构
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 开始填充控制信息记录结构", current_call_id);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		
		// 后续代码保持原有的try-catch结构...
		// [此处省略中间的字符串拷贝代码，保持原有的安全拷贝逻辑]
		
		// 增强的函数调用日志
		try {
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 开始调用__CvtNxMsgToLogStruct函数，参数检查完成", 
				current_call_id);
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
			
			__CvtNxMsgToLogStruct(CtrlInfoSet,TmpNxCmd.list_subfields,pEcIedTb,CtrlInfoSet.nCtrlType);
			
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 成功完成__CvtNxMsgToLogStruct函数调用", 
				current_call_id);
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		} catch(...) {
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 调用__CvtNxMsgToLogStruct函数时发生异常，命令索引=%d", 
				current_call_id, nCmdIndex);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			++iteCmd;
			++nCmdIndex;
			continue;
		}
		
		// 写入数据库
		try {
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 开始调用__WriteLogToDb函数写入数据库", 
				current_call_id);
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
			
			__WriteLogToDb(CtrlInfoSet);
			
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 成功完成__WriteLogToDb函数调用", 
				current_call_id);
			RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		} catch(...) {
			sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 调用__WriteLogToDb函数时发生异常，命令索引=%d", 
				current_call_id, nCmdIndex);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
		}
		
		sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 第%d个命令处理完成", current_call_id, nCmdIndex);
		RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
		
		++iteCmd;
		++nCmdIndex;
	}
	
	sprintf(cErr,"[CALL-%d] __RecordCtrlLogToDb(): 函数执行完成，共处理了%d个命令", 
		current_call_id, nCmdIndex);
	RcdTrcLogWithParentClass(cErr,"CNXEcSrvProOperation");
}
