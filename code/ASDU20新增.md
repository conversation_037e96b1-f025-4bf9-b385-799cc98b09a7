# ASDU20新增设计方案

## 需求分析

根据福建103规约的新增需求，需要实现ASDU20（通用分类命令：远方复归二次设备指示灯）的处理功能，具体要求：

### 主站下发命令格式（ASDU20）
```
启动字符         68H
长度L           
控制域          
类型标识(TYP)     14H
可变结构限定词(VSQ) 81H
传送原因(COT)     14H
ASDU地址（2个字节） 
功能类型(FUN)     FFH
信息序号(INF)     13H
返回信息标识符(RII) 
```

### 子站回应格式（ASDU1）
```
启动字符         68H
长度L          
控制域          
类型标识(TYP)     01H
可变结构限定词(VSQ) 81H
传送原因(COT)     14H/15H 
ASDU地址（2个字节）  
功能类型(FUN)     FFH
信息序号(INF)     13H
双点信息         DPI
7字节时标 CP56Time2a 
附加信息（SIN）    填写主站下发复归帧中的RII
```

## 现状分析

### 现有ASDU20相关代码
- **南网实现**: `TNXEcNW104EXTProAsdu20` 提供了完整的复归命令处理实现模式

### 可复用的现有代码资源

**1. 南网ASDU20实现模式（完整参考）**:
- `TNXEcNW104EXTProAsdu20::_CvtWholeStationRest()` - 全站复归处理
- `TNXEcNW104EXTProAsdu20::_CvtOneDevRest()` - 单装置复归处理
- `TNXEcNW104EXTProAsdu20::MakeCommonMsg_TripRest()` - 复归命令生成

**2. 通用分类命令处理机制**:
- `ASDU21`的通用分类命令框架可直接复用
- `_GetGroupTitleType()` 方法可用于功能类型识别
- 现有的RII（返回信息标识符）处理机制

**3. 地址解析和设备查询**:
- `GetIedBasicCfgByAddr103()` - 103地址到IED映射
- `GetAllIedStatus()` - 获取所有设备状态列表
- `GetSubStationBasicCfg()` - 变电站配置查询

**4. 时间和数据格式转换**:
- `CTimeConvert::GetCP56TIMe()` - CP56Time2a时间格式
- `__FormatAsduHisInfoToFramedata()` - 数据格式化方法

**5. 消息类型常量**（需要确认或新增）:
- 可能需要定义 `NX_IED_CTRL_RESET_ASK` 等复归相关消息类型

## 解决方案

#### 1. 核心优化策略
- **复用南网ASDU20实现**: 直接移植 `TNXEcNW104EXTProAsdu20` 的核心逻辑
- **适配福建103规范**: 调整地址解析逻辑以支持ASDU地址的高8位解析
- **复用通用分类机制**: 借鉴ASDU21的RII和错误处理机制
- **复用现有基础设施**: 利用现有的设备查询、时间转换、日志记录等

#### 2. 具体实现方案

**文件1: NXEcProAsdu20_GWS.h** (基于南网实现优化)
```cpp
/**********************************************************************
* NXEcProAsdu20_GWS.h         author:xxx     date:xxx            
*---------------------------------------------------------------------
*  note: 福建103 ASDU20报文转换处理头文件 - 远方复归二次设备指示灯                                                              
*  
**********************************************************************/

#ifndef _H_NXECPROASDU20_GWS_H_ 
#define _H_NXECPROASDU20_GWS_H_

#include "NXEcProAsdu.h"

/**
* @defgroup  福建103 TNXEcProAsdu20GWS:ASDU20报文转换处理结点类
* @{
*/
 
class TNXEcProAsdu20GWS:public TNXEcProAsdu
{
    ///////////////////////////////////////////////////////////////构造、析构
public:
    
    /**
    * @brief         析构函数
    */
    virtual ~TNXEcProAsdu20GWS();

    /**
    * @brief         构造函数 
    * @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
    * @param[out]    CLogRecord * pLogRecord:日志对象指针
    */
    TNXEcProAsdu20GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);

    ///////////////////////////////////////////////////////////////公用方法
public:
    
    /**
    * @brief         转换规约信息到NX通用消息结构
    * @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
    * @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
    * @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应
    * @return        >=0:成功 <0:失败
    */
    virtual int ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lResult);

    /**
    * @brief         根据NX通用信息及规约命令生成规约结果列表
    * @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
    * @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
    * @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
    * @return        int 0-成功 其它失败
    */
    virtual int ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);

protected:
    
    /**
    * @brief         福建103全站复归处理（复用南网逻辑）
    * @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
    */
    void _CvtWholeStationReset(OUT NX_COMMON_MSG_LIST & lMsg);
    
    /**
    * @brief         福建103单装置复归处理（适配ASDU地址解析）
    * @param[in]     u_int16 nAsduAddr :ASDU地址（高8位为103地址）
    * @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
    */
    void _CvtOneDevReset(IN u_int16 nAsduAddr, OUT NX_COMMON_MSG_LIST & lMsg);
    
    /**
    * @brief         生成设备复归通用消息（复用南网逻辑）
    * @param[in]     int nIedId :设备ID
    * @param[in]     u_int8 nRii :返回信息标识符
    * @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
    * @return        int 0-成功 其它失败
    */
    int _MakeResetCommonMsg(IN int nIedId, IN u_int8 nRii, OUT NX_COMMON_MSG_LIST & lMsg);
    
    /**
    * @brief         复归结果转换为规约回应报文（基于现有_CvtSoftExcResultToPro模式）
    * @param[in]     PRO_FRAME_BODY_LIST & lCmd:原命令列表
    * @param[out]    PRO_FRAME_BODY_LIST & lResult :生成的回应报文列表
    * @return        int 0-成功 其它失败
    */
    int _CvtResetResultToPro(IN PRO_FRAME_BODY_LIST & lCmd, OUT PRO_FRAME_BODY_LIST & lResult);
};

/** @} */ //OVER

#endif  // _H_NXECPROASDU20_GWS_H_
```

**文件2: NXEcProAsdu20_GWS.cpp** (基于南网实现优化)
```cpp
/**********************************************************************
* NXEcProAsdu20_GWS.cpp         author:xxx     date:xxx            
*---------------------------------------------------------------------
*  note: 福建103 ASDU20报文转换处理实现文件 - 远方复归二次设备指示灯                                                             
*  
**********************************************************************/

#include "NXEcProAsdu20_GWS.h"

/**
* @brief         析构函数
*/
TNXEcProAsdu20GWS::~TNXEcProAsdu20GWS()
{
}

/**
* @brief         构造函数 
*/
TNXEcProAsdu20GWS::TNXEcProAsdu20GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
    :TNXEcProAsdu(pSeekIns,pLogRecord)
{
    // 设置类名称
    _SetLogClassName("TNXEcProAsdu20GWS");
}

/**
* @brief         转换规约信息到NX通用消息结构 (复用南网逻辑)
*/
int TNXEcProAsdu20GWS::ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lResult)
{
    // 解析报文中的装置地址，按福建103规范：高8位为103地址，0=子站复归，255=全站复归，1-254=单装置复归
    PRO_FRAME_BODY_LIST::iterator iteCmd = pBodyList->begin();
    while(iteCmd != pBodyList->end())
    {
        if ( iteCmd->nType == 0x14 && iteCmd->nCot == 0x14)  // 复归命令识别
        {
            // 检查FUN和INF是否符合福建103规范
            if (iteCmd->nFun != 0xFF || iteCmd->nInf != 0x13)
            {
                char cError[255];
                sprintf(cError,"ConvertProToCommonMsg():复归命令格式错误,FUN=%02X,INF=%02X(期望FFH,13H)",
                    iteCmd->nFun, iteCmd->nInf);
                RcdErrLogWithParentClass(cError,"TNXEcProAsdu20GWS");
                ++iteCmd;
                continue;
            }
            
            u_int8 nHighByte = (iteCmd->nAddr >> 8) & 0xFF;  // 福建103：ASDU地址高8位
            
            if ( nHighByte == 0 )  // 子站本身复归
            {
                _CvtWholeStationReset(lMsg);
            }
            else if ( nHighByte == 255 )  // 全站复归
            {
                _CvtWholeStationReset(lMsg);
            }
            else if ( nHighByte >= 1 && nHighByte <= 254 )  // 单装置复归
            {
                _CvtOneDevReset(iteCmd->nAddr, lMsg);
            }
        }
        iteCmd++;
    }
    return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         根据NX通用信息生成规约结果列表 (基于现有ConvertCommonMsgToPro实现模式)
*/
int TNXEcProAsdu20GWS::ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
    char cError[255] = "";
    int nRet = EC_PRO_CVT_FAIL;
    m_pCommonMsg = pMsg;
    
    switch(pMsg->n_msg_type)
    {
    case NX_IED_CTRL_SOFTTRAP_CTRL_REP:  // 临时复用软压板控制回应(n_reserve2=1标识复归操作)
        if (pMsg->n_reserve2 == 1) {  // 检查是否为复归操作
            nRet = _CvtResetResultToPro(lCmd, lResult);
        } else {
            sprintf(cError,"ConvertCommonMsgToPro():软压板控制回应但非复归操作,reserve2=%d",pMsg->n_reserve2);
            RcdErrLogWithParentClass(cError,"TNXEcProAsdu20GWS");
            nRet = EC_PRO_CVT_NOSUPPORT;
        }
        break;
    // TODO: 后续可扩展专用复归消息类型
    // case NX_IED_CTRL_RESET_REP:  // 专用复归命令回应
    //     nRet = _CvtResetResultToPro(lCmd, lResult);
    //     break;
    default:
        sprintf(cError,"ConvertCommonMsgToPro():不支持n_msg_type=%d的消息类型",pMsg->n_msg_type);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu20GWS");
        nRet = EC_PRO_CVT_NOSUPPORT;
        break;
    }
    
    m_pCommonMsg = NULL;
    return nRet;
}

/**
* @brief         全站复归处理 (复用南网逻辑)
*/
void TNXEcProAsdu20GWS::_CvtWholeStationReset(OUT NX_COMMON_MSG_LIST & lMsg)
{
    RcdTrcLogWithParentClass("_CvtWholeStationReset():收到全站设备复归命令","TNXEcProAsdu20GWS");

    LIST_IED ListIed;
    m_pModelSeek->GetAllIedStatus(ListIed);
    LIST_IED::iterator iteIed = ListIed.begin();
    while(iteIed != ListIed.end())
    {
        if ( iteIed->e_psrtype != SUB_STATION) //排除子站本身
        {
            if (iteIed->e_opramode == OPRAMODE_RUN) //只针对运行状态的设备
            {
                _MakeResetCommonMsg(iteIed->n_obj_id, 0, lMsg);
            }
        }
        iteIed++;
    }
}

/**
* @brief         单装置复归处理 (适配福建103地址解析)
*/
void TNXEcProAsdu20GWS::_CvtOneDevReset(IN u_int16 nAsduAddr, OUT NX_COMMON_MSG_LIST & lMsg)
{
    char cError[255] = "";
    u_int8 nAddr103 = (nAsduAddr >> 8) & 0xFF;  // 福建103：ASDU地址高8位为103地址
    
    sprintf(cError,"_CvtOneDevReset():收到设备103地址为%d的复归命令",nAddr103);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20GWS");

    const IED_TB * pIed = m_pModelSeek->GetIedBasicCfgByAddr103(nAddr103);
    if (pIed == NULL)
    {
        sprintf(cError,"_CvtOneDevReset():未找到103地址为%d的装置配置",nAddr103);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu20GWS");
        return;
    }

    if (pIed->e_opramode == OPRAMODE_RUN) //只对运行状态的设备进行复归
    {
        _MakeResetCommonMsg(pIed->n_obj_id, 0, lMsg);
    }
}

/**
* @brief         生成设备复归通用消息 (复用南网逻辑，简化实现)
*/
int TNXEcProAsdu20GWS::_MakeResetCommonMsg(IN int nIedId, IN u_int8 nRii, OUT NX_COMMON_MSG_LIST & lMsg)
{
    char cError[255] = "";
    NX_COMMON_MESSAGE CommonMsg;
    
    // 填充通用消息 - 参考南网实现
    CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
    CommonMsg.n_msg_type = NX_IED_CTRL_SOFTTRAP_CTRL;  // 复用软压板控制，后续可扩展专用复归消息类型
    CommonMsg.n_obj_id = nIedId;
    CommonMsg.n_obj_type = NX_OBJ_TYPE_NX_IED;
    CommonMsg.n_sub_obj_id = 0;
    CommonMsg.n_data_src = 0;
    CommonMsg.b_lastmsg = true;
    
    // 使用reserve字段保存RII等信息
    CommonMsg.n_reserve1 = nRii;
    CommonMsg.n_reserve2 = 1;  // 标识为复归操作
    
    // 关键：设置invoke_id保存RII用于回应匹配（参考现有invoke_id机制）
    sprintf(CommonMsg.c_invoke_id, "RESET_RII_%d", nRii);
    
    lMsg.push_back(CommonMsg);
    
    sprintf(cError,"_MakeResetCommonMsg():为设备(IED_ID=%d)生成复归通用消息",nIedId);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20GWS");
    
    return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         复归结果转换为规约回应报文（基于现有_CvtSoftExcResultToPro模式）
*/
int TNXEcProAsdu20GWS::_CvtResetResultToPro(IN PRO_FRAME_BODY_LIST & lCmd, OUT PRO_FRAME_BODY_LIST & lResult)
{
    char cError[255] = "";
    PRO_FRAME_BODY ResponseBody;
    
    // 检查原命令列表
    if (lCmd.empty()) {
        RcdErrLogWithParentClass("_CvtResetResultToPro():原命令列表为空","TNXEcProAsdu20GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    PRO_FRAME_BODY OrigCmd = lCmd.front();
    
    // 获取变电站配置
    const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
    if (pStation == NULL) {
        RcdErrLogWithParentClass("_CvtResetResultToPro():获取变电站配置失败","TNXEcProAsdu20GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    // 构造ASDU1回应报文 - 严格按照福建103规范
    ResponseBody.nType = 0x01;  // ASDU1
    ResponseBody.nVsq = 0x81;
    ResponseBody.nSubstationAdd = pStation->n_outaddr103;
    ResponseBody.nAddr = OrigCmd.nAddr;  // 保持原ASDU地址
    ResponseBody.nCpu = 0;
    ResponseBody.nZone = 0;
    ResponseBody.nFun = 0xFF;
    ResponseBody.nInf = 0x13;
    
    // 根据真实执行结果设置COT和DPI（关键：基于m_pCommonMsg->n_result）
    int nOprResult = ((m_pCommonMsg->n_result == 0) ? 0 : 1);  // 0 success; other failed;
    
    if (nOprResult == 0) {
        ResponseBody.nCot = 0x14;  // 成功 - 肯定认可
        ResponseBody.vVarData.push_back(2);  // DPI=确定（有效）
    } else {
        ResponseBody.nCot = 0x15;  // 失败 - 否定认可  
        ResponseBody.vVarData.push_back(1);  // DPI=不确定（无效）
    }
    
    // 添加7字节时标CP56Time2a
    time_t nCurrentTime = time(NULL);
    CTimeConvert TimeCvt(nCurrentTime, 0);
    string strTime;
    TimeCvt.GetCP56TIMe(strTime);
    ResponseBody.vVarData.insert(ResponseBody.vVarData.end(), strTime.begin(), strTime.end());
    
    // 添加附加信息SIN (原命令的RII) - 关键用于主站匹配
    ResponseBody.vVarData.push_back(OrigCmd.nRii);
    
    lResult.push_back(ResponseBody);
    
    sprintf(cError,"_CvtResetResultToPro():生成复归回应成功,COT=%02X,DPI=%d,RII=%d,执行结果=%d",
        ResponseBody.nCot, (nOprResult == 0) ? 2 : 1, OrigCmd.nRii, m_pCommonMsg->n_result);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20GWS");
    
    return EC_PRO_CVT_SUCCESS;
}
```

/**
* @brief         解析ASDU20复归命令并生成通用消息
*/
int TNXEcProAsdu20GWS::_ParseResetCommand(IN PRO_FRAME_BODY * pBody, OUT NX_COMMON_MESSAGE & CommonMsg)
{
    char cError[255] = "";
    
    if (pBody == NULL)
    {
        return EC_PRO_CVT_FAIL;
    }
    
    // 检查命令格式
    if (pBody->nFun != 0xFF || pBody->nInf != 0x13)
    {
        sprintf(cError,"_ParseResetCommand():复归命令格式错误,FUN=%02X,INF=%02X",
            pBody->nFun, pBody->nInf);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu20GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    // 确定复归目标
    int nTargetType = 0;
    int nIedId = 0;
    int nRet = _DetermineResetTarget(pBody->nAddr, nTargetType, nIedId);
    if (nRet < 0)
    {
        return nRet;
    }
    
    // 填充通用消息
    CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
    CommonMsg.n_msg_type = NX_IED_CTRL_RESET_ASK;  // 假设这是复归命令类型
    CommonMsg.n_obj_id = nIedId;
    CommonMsg.n_obj_type = NX_OBJ_TYPE_NX_IED;
    CommonMsg.n_sub_obj_id = 0; // CPU编号
    CommonMsg.n_data_src = 0;
    CommonMsg.b_lastmsg = true;
    
    // 保存RII用于回应
    CommonMsg.n_reserve1 = pBody->nRii;
    CommonMsg.n_reserve2 = nTargetType;
    
    sprintf(cError,"_ParseResetCommand():解析复归命令成功,目标类型=%d,IED_ID=%d,RII=%d",
        nTargetType, nIedId, pBody->nRii);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20GWS");
    
    return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         根据地址确定复归目标类型
*/
int TNXEcProAsdu20GWS::_DetermineResetTarget(IN u_int16 nAsduAddr, OUT int & nTargetType, OUT int & nIedId)
{
    char cError[255] = "";
    
    u_int8 nHighByte = (nAsduAddr >> 8) & 0xFF;
    
    if (nHighByte == 0)
    {
        // 对子站本身进行复归
        nTargetType = 0;
        nIedId = 0;
        RcdTrcLogWithParentClass("_DetermineResetTarget():复归目标为子站本身","TNXEcProAsdu20GWS");
    }
    else if (nHighByte == 255)
    {
        // 对所有装置进行复归
        nTargetType = 2;
        nIedId = 0;
        RcdTrcLogWithParentClass("_DetermineResetTarget():复归目标为所有装置","TNXEcProAsdu20GWS");
    }
    else if (nHighByte >= 1 && nHighByte <= 254)
    {
        // 对特定装置进行复归
        nTargetType = 1;
        
        // 根据103地址查找IED
        const IED_TB * pIed = m_pModelSeek->GetIedBasicCfgByAddr103(nHighByte);
        if (pIed == NULL)
        {
            sprintf(cError,"_DetermineResetTarget():未找到103地址为%d的装置",nHighByte);
            RcdErrLogWithParentClass(cError,"TNXEcProAsdu20GWS");
            return EC_PRO_CVT_FAIL;
        }
        
        nIedId = pIed->n_obj_id;
        sprintf(cError,"_DetermineResetTarget():复归目标为装置,103地址=%d,IED_ID=%d",
            nHighByte, nIedId);
        RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20GWS");
    }
    else
    {
        sprintf(cError,"_DetermineResetTarget():无效的ASDU地址=%04X",nAsduAddr);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu20GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         生成复归命令回应报文
*/
int TNXEcProAsdu20GWS::_MakeResetResponse(IN PRO_FRAME_BODY * pCmdBody, IN NX_COMMON_MESSAGE * pMsg, OUT PRO_FRAME_BODY_LIST & lResult)
{
    char cError[255] = "";
    PRO_FRAME_BODY ResponseBody;
    
    // 获取变电站配置
    const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
    if (pStation == NULL)
    {
        RcdErrLogWithParentClass("_MakeResetResponse():获取变电站配置失败","TNXEcProAsdu20GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    // 设置回应报文头部
    ResponseBody.nType = 0x01;  // ASDU1
    ResponseBody.nVsq = 0x81;
    ResponseBody.nCot = (pMsg->n_result == 0) ? 0x14 : 0x15;  // 成功14H，失败15H
    ResponseBody.nSubstationAdd = pStation->n_outaddr103;
    ResponseBody.nAddr = pCmdBody->nAddr;
    ResponseBody.nCpu = 0;
    ResponseBody.nZone = 0;
    ResponseBody.nFun = 0xFF;
    ResponseBody.nInf = 0x13;
    ResponseBody.nRii = pCmdBody->nRii;  // 回填原命令的RII
    
    // 设置DPI值
    u_int8 nDpi = 0;
    if (pMsg->n_result == 0)
    {
        nDpi = 2;  // 成功：确定
    }
    else
    {
        nDpi = 1;  // 失败：不确定
    }
    
    // 构造可变数据部分
    ResponseBody.vVarData.clear();
    ResponseBody.vVarData.push_back(nDpi);  // DPI
    
    // 添加7字节时标
    time_t nCurrentTime = time(NULL);
    CTimeConvert TimeCvt(nCurrentTime, 0);
    string strTime;
    TimeCvt.GetCP56TIMe(strTime);
    ResponseBody.vVarData.insert(ResponseBody.vVarData.end(), strTime.begin(), strTime.end());
    
    // 添加附加信息SIN（RII）
    ResponseBody.vVarData.push_back(pCmdBody->nRii);
    
    lResult.push_back(ResponseBody);
    
    sprintf(cError,"_MakeResetResponse():生成复归回应成功,COT=%02X,DPI=%d,RII=%d",
        ResponseBody.nCot, nDpi, pCmdBody->nRii);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20GWS");
    
    return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         检查设备是否支持复归操作
*/
bool TNXEcProAsdu20GWS::__CheckResetSupport(IN int nIedId)
{
    // 这里可以检查设备配置，判断是否支持复归
    // 简化实现：假设所有设备都支持复归
    return true;
}

/**
* @brief         通过软压板方式执行复归操作
*/
int TNXEcProAsdu20GWS::__ExecuteResetBySoftStrap(IN int nIedId, IN int nCpu, OUT bool & bSuccess)
{
    // 这里可以实现通过软压板投退来执行复归操作
    // 简化实现：假设操作成功
    bSuccess = true;
    return EC_PRO_CVT_SUCCESS;
}
```

#### 4. 修改路由配置（关键发现：无需修改）

**重要发现**: 经过代码分析，发现：
1. **TYPE=14H已支持**: `_GetAsdu14CvtType()` 已返回 `CVT_TO_CALL`
2. **路由机制完备**: 转换对象中的switch语句需要添加case 14的处理
3. **文件已存在**: `NXEcProAsdu20_GWS.h/.cpp` 已存在，只需实现内容

**需要修改的文件**: `pro/nx_ec_pro_srv_fj103/NXEc60870CvtObj_GWS.cpp`

在 `ConvertProToCommonMsg()` 方法中添加：
```cpp
case 14: // ASDU20 复归命令 (TYPE=14H)
    strDecs = "远方复归二次设备指示灯操作";
    pAsdu = new TNXEcProAsdu20GWS(m_pModelSeek,m_pLogRecord);
    break;
```