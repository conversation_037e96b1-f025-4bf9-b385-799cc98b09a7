# 观察者-中介者-主题模式学习文档

## 1. 模块概述

### 1.1 核心模块功能

本文档涵盖的模块实现了一个完整的**观察者-中介者-主题**设计模式架构，用于NX电力系统中的消息通信和事件分发。

#### 1.1.1 CNXObserver（观察者）
**位置**：`code/ec_common/NXObserver.h/.cpp`

**主要功能**：
- 封装目标者方发送和获取命令的目标者对象，实现消息的实现
- 向服务中介者注册，订阅感兴趣的事件类型和设备
- 发送命令到目标者（Subject）
- 接收目标者的结果回复和事件通知
- 提供回调机制处理接收到的消息

**在系统中的作用**：
- 作为消息的**消费者**和**发起者**
- 通过中介者与目标者进行解耦通信
- 实现异步消息处理机制

#### 1.1.2 CNXSubject（主题/被观察者）
**位置**：`code/ec_common/NXSubject.h/.cpp`

**主要功能**：
- 响应观察者发送的命令，对应的设备产生事件时通知观察者
- 向服务中介者注册，管理特定的设备列表
- 接收来自观察者的命令请求
- 发送命令执行结果给观察者
- 主动推送事件通知给相关观察者

**在系统中的作用**：
- 作为消息的**处理者**和**事件源**
- 管理具体的业务逻辑和设备操作
- 实现命令-响应模式

#### 1.1.3 CNXEcRegisterObject（注册对象管理）
**位置**：`code/ec_common/NXEcRegisterObject.h/.cpp`

**主要功能**：
- 提供观察者和主题的公共基类功能
- 管理与服务中介者的注册和注销
- 封装共享库的加载和释放
- 实现设备和事件类型的关注度判断
- 提供统一的初始化和清理接口

**在系统中的作用**：
- 作为**基础设施层**，提供通用功能
- 简化观察者和主题的实现复杂度
- 统一管理生命周期

#### 1.1.4 CNXEcSrvMediator（服务中介者）
**位置**：`code/nx_ec_srv_mediator/NXEcSrvMediator.h/.cpp`

**主要功能**：
- 实现对"观察者"和"目标者"行为的封装，使得它们相互不用直接实现通信
- 维护观察者和主题的注册信息
- 路由消息在观察者和主题之间传递
- 管理设备到主题的映射关系
- 提供线程安全的消息分发机制

**在系统中的作用**：
- 作为**通信中枢**，解耦观察者和主题
- 实现消息路由和分发逻辑
- 提供系统级的消息管理服务

### 1.2 模块间协作关系

```mermaid
graph TB
    A[CNXObserver 观察者] --> D[CNXEcSrvMediator 服务中介者]
    B[CNXSubject 主题] --> D
    C[CNXEcRegisterObject 注册对象基类] --> A
    C --> B
    D --> E[消息路由与分发]
    E --> F[设备管理]
    E --> G[事件通知]
    E --> H[命令处理]
```

## 2. 设计模式分析

### 2.1 观察者模式实现

#### 2.1.1 经典观察者模式的变体
本系统实现的是**中介者增强的观察者模式**，与经典观察者模式的区别：

**经典观察者模式**：
```
Subject --> Observer1
        --> Observer2
        --> Observer3
```

**中介者增强的观察者模式**：
```
Subject --> Mediator --> Observer1
                    --> Observer2
                    --> Observer3
```

#### 2.1.2 模式优势
1. **解耦性**：观察者和主题不直接引用，通过中介者通信
2. **可扩展性**：新增观察者或主题无需修改现有代码
3. **灵活性**：支持复杂的消息路由和过滤逻辑
4. **线程安全**：中介者提供统一的线程同步机制

#### 2.1.3 具体实现特点

**订阅机制**：
```cpp
// 观察者设置关注的事件类型
int SetCareEventType(bool bIsCare, EC_INFO_ORDER_LIST& vInfoType);

// 观察者设置关注的设备
int SetCareDev(bool bIsCare, EC_DEV_ORDER_LIST& vDev);
```

**通知机制**：
```cpp
// 主题发送事件通知
int SendEventNotify(NX_EVENT_MESSAGE & EventMsg);

// 中介者分发给相关观察者
int SendEventMsgToObserver(NX_EVENT_MESSAGE & Msg, string &strDes, int nSourceID);
```

### 2.2 中介者模式实现

#### 2.2.1 中介者职责
`CNXEcSrvMediator` 作为中介者，承担以下职责：

1. **注册管理**：
   ```cpp
   bool EnrollObserver(int nOid, const CNXObserver* pObserver, char *chError);
   bool EnrollSubject(int nSid, const CNXSubject* pSubject, char *chError);
   ```

2. **消息路由**：
   ```cpp
   int SendCommonMsgToSubject(int nSubjectID, NX_COMMON_MESSAGE & Msg, string &strDes, int nSourceID);
   int SendCommonMsgToObserver(int nObserverID, NX_COMMON_MESSAGE & Msg, string &strDes, int nSourceID);
   ```

3. **设备映射**：
   ```cpp
   int GetSubjectIdByDevID(int nDevID, DEV_CATEGORY eDevType, string &strDes);
   ```

#### 2.2.2 单例模式实现
中介者采用单例模式确保系统中只有一个消息中心：

```cpp
class CNXEcSrvMediator {
private:
    static CNXEcSrvMediator* sm_pSrvMedIns;
    static CEcSrvMedGarbage sm_SrvMedGarbage;  // 自动清理

public:
    static CNXEcSrvMediator * GetNXEcSrvMedIns(CLogRecord * pLogRecord = NULL, int nReserve= 0);
};
```

### 2.3 注册对象模式

#### 2.3.1 模板方法模式应用
`CNXEcRegisterObject` 使用模板方法模式定义注册流程：

```cpp
// 模板方法：定义初始化算法骨架
bool Init() {
    if(_InitShareLib() != 0) return false;
    if(_RegisterToSrvMediator() != 0) return false;
    return true;
}

// 子类实现具体步骤
virtual int _RegisterToSrvMediator() = 0;
```

#### 2.3.2 策略模式应用
不同类型的注册对象采用不同的注册策略：

```cpp
// 观察者注册策略
int CNXObserver::_RegisterToSrvMediator() {
    return m_pSrvMedLib->EnrollObserver(m_RegObj.nObjID, this, cError);
}

// 主题注册策略
int CNXSubject::_RegisterToSrvMediator() {
    return m_pSrvMedLib->EnrollSubject(m_RegObj.nObjID, this, cError);
}
```

## 3. 类间关系图

### 3.1 整体架构关系

```mermaid
classDiagram
    class CNXEcRegisterObject {
        <<abstract>>
        +Init() bool
        +Exit() bool
        +SetCareEventType() int
        +IsCare() bool
        #_RegisterToSrvMediator()* int
        #_CancelFromSrvMediator()* int
        #m_RegObj MEDIATOR_REG_OBJ_INFO
        #m_pSrvMedLib CNXLoadSrvMedLib*
    }

    class CNXObserver {
        +SendCommand() int
        +ReplyResult() int
        +PushEventNotify() int
        +SetResultCallBack() int
        +SetEventCallBack() int
        -m_pResultHandleCallBack PFUNC_ON_NXCOMMON_MSG_HANDLE
        -m_pEventHandleCallBack PFUNC_ON_NXEVENT_MSG_HANDLE
    }

    class CNXSubject {
        +PushCommand() int
        +SendResult() int
        +SendEventNotify() int
        +SetCmdCallBack() int
        +SetManagerDev() int
        -m_pCmdHandleCallBack PFUNC_ON_NXCOMMON_MSG_HANDLE
    }

    class CNXEcSrvMediator {
        <<singleton>>
        +EnrollObserver() bool
        +EnrollSubject() bool
        +SendCommonMsgToSubject() int
        +SendCommonMsgToObserver() int
        +SendEventMsgToObserver() int
        +GetSubjectIdByDevID() int
        -m_ObserverMap EC_REG_OBJ_MAP
        -m_SubjectMap EC_REG_OBJ_MAP
        -m_DevToSubjectMap EC_DEV2SUBJECT_MAP
    }

    CNXEcRegisterObject <|-- CNXObserver
    CNXEcRegisterObject <|-- CNXSubject
    CNXObserver --> CNXEcSrvMediator : 注册和通信
    CNXSubject --> CNXEcSrvMediator : 注册和通信
    CNXEcSrvMediator --> CNXObserver : 消息分发
    CNXEcSrvMediator --> CNXSubject : 消息分发
```

### 3.2 消息流向关系

```mermaid
sequenceDiagram
    participant O as Observer
    participant M as Mediator
    participant S as Subject

    Note over O,S: 1. 注册阶段
    O->>M: EnrollObserver(id, observer)
    S->>M: EnrollSubject(id, subject)

    Note over O,S: 2. 命令发送
    O->>M: SendCommand(msg, subjectId)
    M->>S: PushCommand(msg, observerId)
    S->>S: 处理命令

    Note over O,S: 3. 结果回复
    S->>M: SendResult(resultMsg)
    M->>O: ReplyResult(resultMsg, subjectId)

    Note over O,S: 4. 事件通知
    S->>M: SendEventNotify(eventMsg)
    M->>O: PushEventNotify(eventMsg, subjectId)

## 4. 业务流程

### 4.1 系统初始化流程

```mermaid
graph TD
    A[系统启动] --> B[初始化服务中介者]
    B --> C[加载共享库]
    C --> D[创建观察者实例]
    C --> E[创建主题实例]
    D --> F[观察者注册到中介者]
    E --> G[主题注册到中介者]
    F --> H[设置关注的事件类型]
    G --> I[设置管理的设备列表]
    H --> J[设置回调函数]
    I --> J
    J --> K[系统就绪]
```

**详细步骤说明**：

1. **服务中介者初始化**：
   ```cpp
   bool InitSrvMed(CLogRecord * pLogRecord, int nReserve) {
       CNXEcSrvMediator * pSrvMed = CNXEcSrvMediator::GetNXEcSrvMedIns(pLogRecord, nReserve);
       return (pSrvMed != NULL);
   }
   ```

2. **观察者初始化**：
   ```cpp
   bool CNXObserver::Init() {
       // 加载服务中介者共享库
       if(_InitShareLib() != 0) return false;

       // 注册到服务中介者
       if(_RegisterToSrvMediator() != 0) return false;

       return true;
   }
   ```

3. **主题初始化**：
   ```cpp
   bool CNXSubject::Init() {
       // 加载服务中介者共享库
       if(_InitShareLib() != 0) return false;

       // 注册到服务中介者
       if(_RegisterToSrvMediator() != 0) return false;

       return true;
   }
   ```

### 4.2 消息注册和订阅流程

#### 4.2.1 观察者订阅流程

```mermaid
graph TD
    A[观察者创建] --> B[设置关注的事件类型]
    B --> C[设置关注的设备列表]
    C --> D[设置事件回调函数]
    D --> E[设置结果回调函数]
    E --> F[调用Init注册]
    F --> G[中介者验证并存储]
    G --> H[订阅完成]
```

**代码实现**：
```cpp
// 1. 设置关注的事件类型
EC_INFO_ORDER_LIST eventTypes;
eventTypes.push_back(NX_IED_EVENT_FILE_REPORT);
eventTypes.push_back(NX_IED_ALARM_FILE_REPORT);
pObserver->SetCareEventType(true, eventTypes);

// 2. 设置关注的设备
EC_DEV_ORDER_LIST devices;
devices.push_back({100, DEV_CATEGORY_IED});
devices.push_back({200, DEV_CATEGORY_IED});
pObserver->SetCareDev(true, devices);

// 3. 设置回调函数
pObserver->SetEventCallBack(this, OnEventReceived);
pObserver->SetResultCallBack(this, OnResultReceived);

// 4. 初始化注册
pObserver->Init();
```

#### 4.2.2 主题注册流程

```mermaid
graph TD
    A[主题创建] --> B[设置管理的设备列表]
    B --> C[设置命令回调函数]
    C --> D[调用Init注册]
    D --> E[中介者建立设备映射]
    E --> F[注册完成]
```

**代码实现**：
```cpp
// 1. 设置管理的设备
EC_DEV_ORDER_LIST managedDevices;
managedDevices.push_back({100, DEV_CATEGORY_IED});
managedDevices.push_back({101, DEV_CATEGORY_IED});
pSubject->SetManagerDev(true, managedDevices);

// 2. 设置命令回调
pSubject->SetCmdCallBack(this, OnCommandReceived);

// 3. 初始化注册
pSubject->Init();
```

### 4.3 消息发布流程

#### 4.3.1 命令发送流程

```mermaid
graph TD
    A[观察者发送命令] --> B[调用SendCommand]
    B --> C[中介者接收命令]
    C --> D[根据设备ID查找目标主题]
    D --> E{找到目标主题?}
    E -->|是| F[调用主题的PushCommand]
    E -->|否| G[返回错误]
    F --> H[主题处理命令]
    H --> I[主题发送结果]
    I --> J[中介者路由结果给观察者]
    J --> K[观察者接收结果]
```

**代码实现**：
```cpp
// 观察者发送命令
int CNXObserver::SendCommand(NX_COMMON_MESSAGE & Msg) {
    string strDes;
    int nSubjectID = m_pSrvMedLib->GetSubjectIdByDevID(Msg.n_obj_id, (DEV_CATEGORY)Msg.n_obj_type, strDes);

    if(nSubjectID == USER_DEF_INVALID_ID) {
        return EC_SRV_MED_NOSUBJECT;
    }

    return m_pSrvMedLib->SendCommonMsgToSubject(nSubjectID, Msg, strDes, m_RegObj.nObjID);
}

// 中介者路由命令
int CNXEcSrvMediator::SendCommonMsgToSubject(int nSubjectID, NX_COMMON_MESSAGE & Msg, string &strDes, int nSourceID) {
    EC_REG_OBJ_MAP::iterator it = m_SubjectMap.find(nSubjectID);
    if(it == m_SubjectMap.end()) {
        return EC_SRV_MED_NOSUBJECT;
    }

    CNXSubject * pSubject = (CNXSubject *)it->second.pRegObj;
    return pSubject->PushCommand(Msg, nSourceID);
}
```

#### 4.3.2 事件通知流程

```mermaid
graph TD
    A[主题产生事件] --> B[调用SendEventNotify]
    B --> C[中介者接收事件]
    C --> D[遍历所有观察者]
    D --> E{观察者关注此事件?}
    E -->|是| F{观察者关注此设备?}
    E -->|否| G[跳过此观察者]
    F -->|是| H[调用观察者PushEventNotify]
    F -->|否| G
    H --> I[观察者处理事件]
    G --> J{还有观察者?}
    J -->|是| D
    J -->|否| K[通知完成]
```

**代码实现**：
```cpp
// 主题发送事件通知
int CNXSubject::SendEventNotify(NX_EVENT_MESSAGE & EventMsg) {
    string strDes;
    return m_pSrvMedLib->SendEventMsgToObserver(EventMsg, strDes, m_RegObj.nObjID);
}

// 中介者分发事件
int CNXEcSrvMediator::SendEventMsgToObserver(NX_EVENT_MESSAGE & Msg, string &strDes, int nSourceID) {
    int nRet = 0;
    EC_REG_OBJ_MAP::iterator it;

    for(it = m_ObserverMap.begin(); it != m_ObserverMap.end(); it++) {
        CNXObserver * pObserver = (CNXObserver *)it->second.pRegObj;

        // 检查观察者是否关注此事件类型和设备
        if(pObserver->IsCare(Msg.n_msg_type, Msg.n_event_obj, (DEV_CATEGORY)Msg.n_event_obj_type)) {
            pObserver->PushEventNotify(Msg, nSourceID);
        }
    }

    return nRet;
}
```

### 4.4 回调触发流程

#### 4.4.1 事件回调触发

```mermaid
graph TD
    A[中介者调用PushEventNotify] --> B[观察者接收事件]
    B --> C{事件回调函数已设置?}
    C -->|是| D[调用用户事件回调]
    C -->|否| E[忽略事件]
    D --> F[用户处理事件]
    F --> G[回调完成]
```

**代码实现**：
```cpp
int CNXObserver::PushEventNotify(NX_EVENT_MESSAGE & EventMsg, int nSourceID) {
    if(m_pEventHandleCallBack != NULL && m_pEventHandleCallBackObj != NULL) {
        return m_pEventHandleCallBack(m_pEventHandleCallBackObj, EventMsg);
    }
    return 0;
}
```

#### 4.4.2 命令回调触发

```mermaid
graph TD
    A[中介者调用PushCommand] --> B[主题接收命令]
    B --> C{命令回调函数已设置?}
    C -->|是| D[调用用户命令回调]
    C -->|否| E[返回未处理]
    D --> F[用户处理命令]
    F --> G[生成处理结果]
    G --> H[调用SendResult发送结果]
```

**代码实现**：
```cpp
int CNXSubject::PushCommand(NX_COMMON_MESSAGE & Msg, int nSourceID) {
    if(m_pCmdHandleCallBack != NULL && m_pCmdHandleCallBackObj != NULL) {
        return m_pCmdHandleCallBack(m_pCmdHandleCallBackObj, Msg);
    }
    return EC_SRV_MED_NOCALLBACK;
}

## 5. 回调机制

### 5.1 回调函数类型定义

系统定义了两种主要的回调函数类型：

#### 5.1.1 事件消息回调
```cpp
/** @brief 系统事件消息处理回调函数类型 */
typedef int (*PFUNC_ON_NXEVENT_MSG_HANDLE)(IN LPVOID pObj, IN NX_EVENT_MESSAGE &eventMsg);
```

**参数说明**：
- `pObj`：回调对象指针，通常是调用者的this指针
- `eventMsg`：事件消息结构体，包含完整的事件信息

**返回值**：
- `0`：处理成功
- `非0`：处理失败

#### 5.1.2 通用消息回调
```cpp
/** @brief 系统通用消息处理回调函数类型 */
typedef int (*PFUNC_ON_NXCOMMON_MSG_HANDLE)(IN LPVOID pObj, IN NX_COMMON_MESSAGE &commonMsg);
```

**参数说明**：
- `pObj`：回调对象指针
- `commonMsg`：通用消息结构体，包含命令或结果信息

**返回值**：
- `0`：处理成功
- `非0`：处理失败

### 5.2 回调注册机制

#### 5.2.1 观察者回调注册

**事件回调注册**：
```cpp
int CNXObserver::SetEventCallBack(LPVOID pCallBackObj, PFUNC_ON_NXEVENT_MSG_HANDLE pCallBack) {
    if(pCallBackObj == NULL || pCallBack == NULL) {
        return EC_SRV_MED_PARAMERROR;
    }

    m_pEventHandleCallBackObj = pCallBackObj;
    m_pEventHandleCallBack = pCallBack;
    return 0;
}
```

**结果回调注册**：
```cpp
int CNXObserver::SetResultCallBack(LPVOID pCallBackObj, PFUNC_ON_NXCOMMON_MSG_HANDLE pCallBack) {
    if(pCallBackObj == NULL || pCallBack == NULL) {
        return EC_SRV_MED_PARAMERROR;
    }

    m_pResultHandleCallBackObj = pCallBackObj;
    m_pResultHandleCallBack = pCallBack;
    return 0;
}
```

#### 5.2.2 主题回调注册

**命令回调注册**：
```cpp
int CNXSubject::SetCmdCallBack(LPVOID pCallBackObj, PFUNC_ON_NXCOMMON_MSG_HANDLE pCallBack) {
    if(pCallBackObj == NULL || pCallBack == NULL) {
        return EC_SRV_MED_PARAMERROR;
    }

    m_pCmdHandleCallBackObj = pCallBackObj;
    m_pCmdHandleCallBack = pCallBack;
    return 0;
}
```

### 5.3 回调触发机制

#### 5.3.1 事件回调触发时机

```mermaid
graph TD
    A[主题产生事件] --> B[调用SendEventNotify]
    B --> C[中介者分发事件]
    C --> D[检查观察者关注度]
    D --> E[调用观察者PushEventNotify]
    E --> F[触发用户事件回调]
    F --> G[用户处理事件]
```

**触发代码**：
```cpp
int CNXObserver::PushEventNotify(NX_EVENT_MESSAGE & EventMsg, int nSourceID) {
    // 记录日志
    sprintf(cInfo, "收到来自主题ID=%d的事件通知，事件类型=%d，设备ID=%d",
            nSourceID, EventMsg.n_msg_type, EventMsg.n_event_obj);
    RcdTrcLogWithParentClass(cInfo, "CNXObserver");

    // 触发用户回调
    if(m_pEventHandleCallBack != NULL && m_pEventHandleCallBackObj != NULL) {
        return m_pEventHandleCallBack(m_pEventHandleCallBackObj, EventMsg);
    }

    return 0;
}
```

#### 5.3.2 命令回调触发时机

```mermaid
graph TD
    A[观察者发送命令] --> B[中介者路由命令]
    B --> C[找到目标主题]
    C --> D[调用主题PushCommand]
    D --> E[触发用户命令回调]
    E --> F[用户处理命令]
    F --> G[生成结果]
    G --> H[调用SendResult]
```

**触发代码**：
```cpp
int CNXSubject::PushCommand(NX_COMMON_MESSAGE & Msg, int nSourceID) {
    // 记录日志
    sprintf(cInfo, "收到来自观察者ID=%d的命令，命令类型=%d，目标设备=%d",
            nSourceID, Msg.n_msg_type, Msg.n_obj_id);
    RcdTrcLogWithParentClass(cInfo, "CNXSubject");

    // 触发用户回调
    if(m_pCmdHandleCallBack != NULL && m_pCmdHandleCallBackObj != NULL) {
        return m_pCmdHandleCallBack(m_pCmdHandleCallBackObj, Msg);
    }

    RcdErrLogWithParentClass("命令回调函数未设置", "CNXSubject");
    return EC_SRV_MED_NOCALLBACK;
}
```

#### 5.3.3 结果回调触发时机

```mermaid
graph TD
    A[主题处理完命令] --> B[调用SendResult]
    B --> C[中介者路由结果]
    C --> D[找到原始观察者]
    D --> E[调用观察者ReplyResult]
    E --> F[触发用户结果回调]
    F --> G[用户处理结果]
```

**触发代码**：
```cpp
int CNXObserver::ReplyResult(NX_COMMON_MESSAGE & ResultMsg, int nSourceID) {
    // 记录日志
    sprintf(cInfo, "收到来自主题ID=%d的命令结果，结果=%d",
            nSourceID, ResultMsg.n_result);
    RcdTrcLogWithParentClass(cInfo, "CNXObserver");

    // 触发用户回调
    if(m_pResultHandleCallBack != NULL && m_pResultHandleCallBackObj != NULL) {
        return m_pResultHandleCallBack(m_pResultHandleCallBackObj, ResultMsg);
    }

    return 0;
}
```

### 5.4 扩展回调的实现方式

#### 5.4.1 自定义回调接口

用户可以通过继承或组合的方式实现自定义回调：

**继承方式**：
```cpp
class MyObserver : public CNXObserver {
public:
    bool Init() {
        // 设置回调为自己的成员函数
        SetEventCallBack(this, MyEventHandler);
        SetResultCallBack(this, MyResultHandler);
        return CNXObserver::Init();
    }

private:
    static int MyEventHandler(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        MyObserver* pThis = (MyObserver*)pObj;
        return pThis->HandleEvent(eventMsg);
    }

    static int MyResultHandler(LPVOID pObj, NX_COMMON_MESSAGE &resultMsg) {
        MyObserver* pThis = (MyObserver*)pObj;
        return pThis->HandleResult(resultMsg);
    }

    int HandleEvent(NX_EVENT_MESSAGE &eventMsg) {
        // 自定义事件处理逻辑
        return 0;
    }

    int HandleResult(NX_COMMON_MESSAGE &resultMsg) {
        // 自定义结果处理逻辑
        return 0;
    }
};
```

**组合方式**：
```cpp
class MyApplication {
private:
    CNXObserver* m_pObserver;

public:
    bool InitObserver() {
        m_pObserver = new CNXObserver();
        m_pObserver->SetEventCallBack(this, OnEventReceived);
        m_pObserver->SetResultCallBack(this, OnResultReceived);
        return m_pObserver->Init();
    }

    static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        MyApplication* pApp = (MyApplication*)pObj;
        return pApp->ProcessEvent(eventMsg);
    }

    static int OnResultReceived(LPVOID pObj, NX_COMMON_MESSAGE &resultMsg) {
        MyApplication* pApp = (MyApplication*)pObj;
        return pApp->ProcessResult(resultMsg);
    }

private:
    int ProcessEvent(NX_EVENT_MESSAGE &eventMsg) {
        // 应用层事件处理逻辑
        return 0;
    }

    int ProcessResult(NX_COMMON_MESSAGE &resultMsg) {
        // 应用层结果处理逻辑
        return 0;
    }
};
```

#### 5.4.2 回调链模式

支持多级回调处理：

```cpp
class CallbackChain {
private:
    vector<PFUNC_ON_NXEVENT_MSG_HANDLE> m_eventHandlers;
    vector<LPVOID> m_eventObjects;

public:
    void AddEventHandler(LPVOID pObj, PFUNC_ON_NXEVENT_MSG_HANDLE handler) {
        m_eventObjects.push_back(pObj);
        m_eventHandlers.push_back(handler);
    }

    static int ChainEventHandler(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        CallbackChain* pChain = (CallbackChain*)pObj;

        for(size_t i = 0; i < pChain->m_eventHandlers.size(); i++) {
            int result = pChain->m_eventHandlers[i](pChain->m_eventObjects[i], eventMsg);
            if(result != 0) {
                return result;  // 链中断
            }
        }

        return 0;
    }
};

## 6. 接口设计

### 6.1 CNXObserver 主要接口

#### 6.1.1 核心通信接口

**SendCommand - 发送命令**
```cpp
int SendCommand(NX_COMMON_MESSAGE & Msg);
```
- **功能**：向目标主题发送命令消息
- **参数**：`Msg` - 通用消息结构，包含命令信息
- **返回值**：
  - `0` - 发送成功
  - `EC_SRV_MED_NOSUBJECT` - 未找到目标主题
  - `EC_SRV_MED_PARAMERROR` - 参数错误
- **使用场景**：远程控制、参数设置、状态查询等

**ReplyResult - 接收结果**
```cpp
int ReplyResult(NX_COMMON_MESSAGE & ResultMsg, int nSourceID);
```
- **功能**：接收来自主题的命令执行结果
- **参数**：
  - `ResultMsg` - 结果消息
  - `nSourceID` - 源主题ID
- **返回值**：`0` - 处理成功
- **使用场景**：命令执行结果处理

**PushEventNotify - 接收事件通知**
```cpp
int PushEventNotify(NX_EVENT_MESSAGE & EventMsg, int nSourceID);
```
- **功能**：接收来自主题的事件通知
- **参数**：
  - `EventMsg` - 事件消息
  - `nSourceID` - 源主题ID
- **返回值**：`0` - 处理成功
- **使用场景**：事件监控、状态变化通知

#### 6.1.2 配置接口

**SetCareEventType - 设置关注的事件类型**
```cpp
int SetCareEventType(bool bIsCare, EC_INFO_ORDER_LIST& vInfoType);
```
- **功能**：配置观察者关注的事件类型列表
- **参数**：
  - `bIsCare` - true表示关注列表中的类型，false表示不关注
  - `vInfoType` - 事件类型列表
- **返回值**：`0` - 设置成功
- **使用场景**：订阅特定类型的事件

**SetCareDev - 设置关注的设备**
```cpp
int SetCareDev(bool bIsCare, EC_DEV_ORDER_LIST& vDev);
```
- **功能**：配置观察者关注的设备列表
- **参数**：
  - `bIsCare` - true表示关注列表中的设备，false表示不关注
  - `vDev` - 设备列表
- **返回值**：`0` - 设置成功
- **使用场景**：订阅特定设备的事件

#### 6.1.3 回调设置接口

**SetEventCallBack - 设置事件回调**
```cpp
int SetEventCallBack(LPVOID pCallBackObj, PFUNC_ON_NXEVENT_MSG_HANDLE pCallBack);
```
- **功能**：设置事件消息处理回调函数
- **参数**：
  - `pCallBackObj` - 回调对象指针
  - `pCallBack` - 回调函数指针
- **返回值**：`0` - 设置成功
- **使用场景**：注册事件处理逻辑

**SetResultCallBack - 设置结果回调**
```cpp
int SetResultCallBack(LPVOID pCallBackObj, PFUNC_ON_NXCOMMON_MSG_HANDLE pCallBack);
```
- **功能**：设置命令结果处理回调函数
- **参数**：
  - `pCallBackObj` - 回调对象指针
  - `pCallBack` - 回调函数指针
- **返回值**：`0` - 设置成功
- **使用场景**：注册结果处理逻辑

### 6.2 CNXSubject 主要接口

#### 6.2.1 核心通信接口

**PushCommand - 接收命令**
```cpp
int PushCommand(NX_COMMON_MESSAGE & Msg, int nSourceID);
```
- **功能**：接收来自观察者的命令消息
- **参数**：
  - `Msg` - 命令消息
  - `nSourceID` - 源观察者ID
- **返回值**：`0` - 处理成功
- **使用场景**：命令处理入口

**SendResult - 发送结果**
```cpp
int SendResult(NX_COMMON_MESSAGE & ResultMsg);
```
- **功能**：向观察者发送命令执行结果
- **参数**：`ResultMsg` - 结果消息
- **返回值**：`0` - 发送成功
- **使用场景**：命令执行完成后返回结果

**SendEventNotify - 发送事件通知**
```cpp
int SendEventNotify(NX_EVENT_MESSAGE & EventMsg);
```
- **功能**：向相关观察者发送事件通知
- **参数**：`EventMsg` - 事件消息
- **返回值**：`0` - 发送成功
- **使用场景**：主动推送事件信息

#### 6.2.2 配置接口

**SetManagerDev - 设置管理的设备**
```cpp
int SetManagerDev(bool bIsManager, EC_DEV_ORDER_LIST& vDev);
```
- **功能**：配置主题管理的设备列表
- **参数**：
  - `bIsManager` - true表示管理列表中的设备
  - `vDev` - 设备列表
- **返回值**：`0` - 设置成功
- **使用场景**：定义主题的管理范围

#### 6.2.3 回调设置接口

**SetCmdCallBack - 设置命令回调**
```cpp
int SetCmdCallBack(LPVOID pCallBackObj, PFUNC_ON_NXCOMMON_MSG_HANDLE pCallBack);
```
- **功能**：设置命令消息处理回调函数
- **参数**：
  - `pCallBackObj` - 回调对象指针
  - `pCallBack` - 回调函数指针
- **返回值**：`0` - 设置成功
- **使用场景**：注册命令处理逻辑

### 6.3 CNXEcSrvMediator 主要接口

#### 6.3.1 注册管理接口

**EnrollObserver - 注册观察者**
```cpp
bool EnrollObserver(int nOid, const CNXObserver* pObserver, char *chError);
```
- **功能**：向中介者注册观察者
- **参数**：
  - `nOid` - 观察者唯一ID
  - `pObserver` - 观察者对象指针
  - `chError` - 错误信息输出缓冲区
- **返回值**：`true` - 注册成功，`false` - 注册失败
- **使用场景**：观察者初始化时调用

**EnrollSubject - 注册主题**
```cpp
bool EnrollSubject(int nSid, const CNXSubject* pSubject, char *chError);
```
- **功能**：向中介者注册主题
- **参数**：
  - `nSid` - 主题唯一ID
  - `pSubject` - 主题对象指针
  - `chError` - 错误信息输出缓冲区
- **返回值**：`true` - 注册成功，`false` - 注册失败
- **使用场景**：主题初始化时调用

#### 6.3.2 消息路由接口

**SendCommonMsgToSubject - 向主题发送消息**
```cpp
int SendCommonMsgToSubject(int nSubjectID, NX_COMMON_MESSAGE & Msg, string &strDes, int nSourceID);
```
- **功能**：将消息路由到指定主题
- **参数**：
  - `nSubjectID` - 目标主题ID
  - `Msg` - 消息内容
  - `strDes` - 描述信息输出
  - `nSourceID` - 源观察者ID
- **返回值**：`0` - 路由成功
- **使用场景**：观察者发送命令时调用

**SendCommonMsgToObserver - 向观察者发送消息**
```cpp
int SendCommonMsgToObserver(int nObserverID, NX_COMMON_MESSAGE & Msg, string &strDes, int nSourceID);
```
- **功能**：将消息路由到指定观察者
- **参数**：
  - `nObserverID` - 目标观察者ID
  - `Msg` - 消息内容
  - `strDes` - 描述信息输出
  - `nSourceID` - 源主题ID
- **返回值**：`0` - 路由成功
- **使用场景**：主题发送结果时调用

**SendEventMsgToObserver - 向观察者发送事件**
```cpp
int SendEventMsgToObserver(NX_EVENT_MESSAGE & Msg, string &strDes, int nSourceID);
```
- **功能**：将事件消息分发给相关观察者
- **参数**：
  - `Msg` - 事件消息
  - `strDes` - 描述信息输出
  - `nSourceID` - 源主题ID
- **返回值**：`0` - 分发成功
- **使用场景**：主题发送事件通知时调用

#### 6.3.3 查询接口

**GetSubjectIdByDevID - 根据设备ID获取主题ID**
```cpp
int GetSubjectIdByDevID(int nDevID, DEV_CATEGORY eDevType, string &strDes);
```
- **功能**：根据设备信息查找对应的主题ID
- **参数**：
  - `nDevID` - 设备ID
  - `eDevType` - 设备类型
  - `strDes` - 描述信息输出
- **返回值**：主题ID，失败时返回`USER_DEF_INVALID_ID`
- **使用场景**：观察者发送命令前查找目标主题

## 7. 使用示例

### 7.1 创建和配置观察者

#### 7.1.1 基本观察者实现

```cpp
class MyObserver {
private:
    CNXObserver* m_pObserver;

public:
    MyObserver() : m_pObserver(nullptr) {}

    ~MyObserver() {
        if(m_pObserver) {
            m_pObserver->Exit();
            delete m_pObserver;
        }
    }

    bool Initialize() {
        // 1. 创建观察者实例
        m_pObserver = new CNXObserver();

        // 2. 设置观察者ID和描述
        MEDIATOR_REG_OBJ_INFO regInfo;
        regInfo.nObjID = BUS_SWAP_OBSERVER_ID;  // 使用预定义ID
        strcpy(regInfo.cObjDesc, "总线交换观察者");
        regInfo.pRegObj = m_pObserver;
        m_pObserver->SetRegObjInfo(regInfo);

        // 3. 设置关注的事件类型
        EC_INFO_ORDER_LIST eventTypes;
        eventTypes.push_back(NX_IED_EVENT_FILE_REPORT);      // 事件报告
        eventTypes.push_back(NX_IED_ALARM_FILE_REPORT);      // 告警报告
        eventTypes.push_back(NX_IED_CTRL_SG_EXC_REP);        // 单点执行回复
        m_pObserver->SetCareEventType(true, eventTypes);

        // 4. 设置关注的设备
        EC_DEV_ORDER_LIST devices;
        devices.push_back({100, DEV_CATEGORY_IED});  // IED设备100
        devices.push_back({101, DEV_CATEGORY_IED});  // IED设备101
        devices.push_back({ALL_DEV, DEV_CATEGORY_IED}); // 所有IED设备
        m_pObserver->SetCareDev(true, devices);

        // 5. 设置回调函数
        m_pObserver->SetEventCallBack(this, OnEventReceived);
        m_pObserver->SetResultCallBack(this, OnResultReceived);

        // 6. 初始化并注册到中介者
        if(!m_pObserver->Init()) {
            delete m_pObserver;
            m_pObserver = nullptr;
            return false;
        }

        return true;
    }

    // 事件回调处理函数
    static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        MyObserver* pThis = (MyObserver*)pObj;
        return pThis->HandleEvent(eventMsg);
    }

    // 结果回调处理函数
    static int OnResultReceived(LPVOID pObj, NX_COMMON_MESSAGE &resultMsg) {
        MyObserver* pThis = (MyObserver*)pObj;
        return pThis->HandleResult(resultMsg);
    }

private:
    int HandleEvent(NX_EVENT_MESSAGE &eventMsg) {
        printf("收到事件：设备ID=%d, 事件类型=%d, 时间=%u\n",
               eventMsg.n_event_obj, eventMsg.n_msg_type, eventMsg.n_send_utctm);

        // 根据事件类型进行不同处理
        switch(eventMsg.n_msg_type) {
            case NX_IED_EVENT_FILE_REPORT:
                ProcessEventReport(eventMsg);
                break;
            case NX_IED_ALARM_FILE_REPORT:
                ProcessAlarmReport(eventMsg);
                break;
            default:
                printf("未处理的事件类型：%d\n", eventMsg.n_msg_type);
                break;
        }

        return 0;
    }

    int HandleResult(NX_COMMON_MESSAGE &resultMsg) {
        printf("收到命令结果：设备ID=%d, 命令类型=%d, 结果=%d\n",
               resultMsg.n_obj_id, resultMsg.n_msg_type, resultMsg.n_result);

        // 处理命令执行结果
        if(resultMsg.n_result == 0) {
            printf("命令执行成功\n");
        } else {
            printf("命令执行失败，错误码：%d\n", resultMsg.n_result);
        }

        return 0;
    }

    void ProcessEventReport(NX_EVENT_MESSAGE &eventMsg) {
        // 处理事件报告的具体逻辑
        printf("处理事件报告...\n");
    }

    void ProcessAlarmReport(NX_EVENT_MESSAGE &eventMsg) {
        // 处理告警报告的具体逻辑
        printf("处理告警报告...\n");
    }

public:
    // 发送控制命令示例
    bool SendControlCommand(int deviceId, int commandType, float value) {
        if(!m_pObserver) return false;

        NX_COMMON_MESSAGE cmdMsg;
        memset(&cmdMsg, 0, sizeof(cmdMsg));

        // 填充消息头
        strcpy(cmdMsg.c_src_name, "MyObserver");
        strcpy(cmdMsg.c_dst_name, "broadcast");
        cmdMsg.n_send_utctm = time(NULL);
        cmdMsg.n_msg_topic = NX_TOPIC_COMMAND;
        cmdMsg.n_msg_type = commandType;

        // 填充目标信息
        cmdMsg.n_obj_id = deviceId;
        cmdMsg.n_obj_type = NX_OBJ_TYPE_NX_IED;
        cmdMsg.n_data_src = 2;  // 来自远方主站
        cmdMsg.b_lastmsg = true;

        // 添加控制值
        NX_COMMON_FIELD_STRUCT fieldStruct;
        fieldStruct.n_field_type = NX_FIELD_TYPE_CTRL_VALUE;
        fieldStruct.f_value = value;
        cmdMsg.list_subfields.push_back(fieldStruct);

        // 发送命令
        int result = m_pObserver->SendCommand(cmdMsg);

        // 清理资源
        cmdMsg.list_subfields.clear();

        return (result == 0);
    }
};
```

### 7.2 创建和配置主题

#### 7.2.1 基本主题实现

```cpp
class MySubject {
private:
    CNXSubject* m_pSubject;

public:
    MySubject() : m_pSubject(nullptr) {}

    ~MySubject() {
        if(m_pSubject) {
            m_pSubject->Exit();
            delete m_pSubject;
        }
    }

    bool Initialize() {
        // 1. 创建主题实例
        m_pSubject = new CNXSubject();

        // 2. 设置主题ID和描述
        MEDIATOR_REG_OBJ_INFO regInfo;
        regInfo.nObjID = MAIN_CTL_SUBJECT_ID;  // 使用预定义ID
        strcpy(regInfo.cObjDesc, "主控制主题");
        regInfo.pRegObj = m_pSubject;
        m_pSubject->SetRegObjInfo(regInfo);

        // 3. 设置管理的设备
        EC_DEV_ORDER_LIST managedDevices;
        managedDevices.push_back({100, DEV_CATEGORY_IED});
        managedDevices.push_back({101, DEV_CATEGORY_IED});
        managedDevices.push_back({102, DEV_CATEGORY_IED});
        m_pSubject->SetManagerDev(true, managedDevices);

        // 4. 设置命令回调函数
        m_pSubject->SetCmdCallBack(this, OnCommandReceived);

        // 5. 初始化并注册到中介者
        if(!m_pSubject->Init()) {
            delete m_pSubject;
            m_pSubject = nullptr;
            return false;
        }

        return true;
    }

    // 命令回调处理函数
    static int OnCommandReceived(LPVOID pObj, NX_COMMON_MESSAGE &cmdMsg) {
        MySubject* pThis = (MySubject*)pObj;
        return pThis->HandleCommand(cmdMsg);
    }

private:
    int HandleCommand(NX_COMMON_MESSAGE &cmdMsg) {
        printf("收到命令：设备ID=%d, 命令类型=%d\n",
               cmdMsg.n_obj_id, cmdMsg.n_msg_type);

        // 模拟命令处理
        bool success = ProcessCommand(cmdMsg);

        // 发送处理结果
        SendCommandResult(cmdMsg, success);

        // 如果命令执行成功，可能需要发送事件通知
        if(success) {
            SendEventNotification(cmdMsg);
        }

        return 0;
    }

    bool ProcessCommand(NX_COMMON_MESSAGE &cmdMsg) {
        // 根据命令类型进行不同处理
        switch(cmdMsg.n_msg_type) {
            case NX_IED_CTRL_SG_EXC_ASK:  // 单点执行
                return ProcessSinglePointControl(cmdMsg);
            case NX_IED_CTRL_SOFTSTRAP_EXC_ASK:  // 软压板执行
                return ProcessSoftStrapControl(cmdMsg);
            case NX_IED_CALL_HARDSTRAP_ASK:  // 召唤硬压板
                return ProcessHardStrapCall(cmdMsg);
            default:
                printf("不支持的命令类型：%d\n", cmdMsg.n_msg_type);
                return false;
        }
    }

    bool ProcessSinglePointControl(NX_COMMON_MESSAGE &cmdMsg) {
        printf("处理单点控制命令...\n");
        // 模拟控制逻辑
        usleep(100000);  // 模拟处理时间
        return true;  // 假设执行成功
    }

    bool ProcessSoftStrapControl(NX_COMMON_MESSAGE &cmdMsg) {
        printf("处理软压板控制命令...\n");
        usleep(50000);
        return true;
    }

    bool ProcessHardStrapCall(NX_COMMON_MESSAGE &cmdMsg) {
        printf("处理硬压板召唤命令...\n");
        usleep(200000);
        return true;
    }

    void SendCommandResult(NX_COMMON_MESSAGE &originalCmd, bool success) {
        NX_COMMON_MESSAGE resultMsg = originalCmd;  // 复制原命令

        // 修改为结果消息
        resultMsg.n_msg_type = GetResultMsgType(originalCmd.n_msg_type);
        resultMsg.n_result = success ? 0 : 1;  // 0-成功，1-失败
        resultMsg.n_send_utctm = time(NULL);

        // 发送结果
        m_pSubject->SendResult(resultMsg);

        printf("发送命令结果：%s\n", success ? "成功" : "失败");
    }

    void SendEventNotification(NX_COMMON_MESSAGE &cmdMsg) {
        NX_EVENT_MESSAGE eventMsg;
        memset(&eventMsg, 0, sizeof(eventMsg));

        // 填充事件消息
        strcpy(eventMsg.c_src_name, "MySubject");
        strcpy(eventMsg.c_dst_name, "broadcast");
        eventMsg.n_send_utctm = time(NULL);
        eventMsg.n_msg_topic = NX_TOPIC_EVENT;
        eventMsg.n_msg_type = NX_IED_EVENT_FILE_REPORT;
        eventMsg.n_event_obj = cmdMsg.n_obj_id;
        eventMsg.n_event_obj_type = cmdMsg.n_obj_type;

        // 添加事件字段
        NX_EVENT_FIELD_STRUCT eventField;
        eventField.n_field_type = NX_FIELD_TYPE_EVENT_DESC;
        strcpy(eventField.c_value, "命令执行完成");
        eventMsg.list_subfields.push_back(eventField);

        // 发送事件通知
        m_pSubject->SendEventNotify(eventMsg);

        // 清理资源
        eventMsg.list_subfields.clear();

        printf("发送事件通知：命令执行完成\n");
    }

    int GetResultMsgType(int cmdMsgType) {
        // 将命令类型转换为对应的结果类型
        switch(cmdMsgType) {
            case NX_IED_CTRL_SG_EXC_ASK:
                return NX_IED_CTRL_SG_EXC_REP;
            case NX_IED_CTRL_SOFTSTRAP_EXC_ASK:
                return NX_IED_CTRL_SOFTSTRAP_EXC_REP;
            case NX_IED_CALL_HARDSTRAP_ASK:
                return NX_IED_CALL_HARDSTRAP_REP;
            default:
                return cmdMsgType + 1000;  // 简单的转换规则
        }
    }
};
```

### 7.3 完整的应用示例

#### 7.3.1 系统初始化和运行

```cpp
class MessageSystem {
private:
    MyObserver* m_pObserver;
    MySubject* m_pSubject;

public:
    MessageSystem() : m_pObserver(nullptr), m_pSubject(nullptr) {}

    ~MessageSystem() {
        Cleanup();
    }

    bool Initialize() {
        // 1. 初始化服务中介者
        if(!InitSrvMed()) {
            printf("初始化服务中介者失败\n");
            return false;
        }

        // 2. 创建并初始化主题
        m_pSubject = new MySubject();
        if(!m_pSubject->Initialize()) {
            printf("初始化主题失败\n");
            return false;
        }

        // 3. 创建并初始化观察者
        m_pObserver = new MyObserver();
        if(!m_pObserver->Initialize()) {
            printf("初始化观察者失败\n");
            return false;
        }

        printf("消息系统初始化成功\n");
        return true;
    }

    void RunDemo() {
        printf("开始运行演示...\n");

        // 发送几个测试命令
        printf("\n=== 发送单点控制命令 ===\n");
        m_pObserver->SendControlCommand(100, NX_IED_CTRL_SG_EXC_ASK, 1.0f);

        sleep(1);  // 等待处理完成

        printf("\n=== 发送软压板控制命令 ===\n");
        m_pObserver->SendControlCommand(101, NX_IED_CTRL_SOFTSTRAP_EXC_ASK, 0.0f);

        sleep(1);

        printf("\n=== 发送硬压板召唤命令 ===\n");
        m_pObserver->SendControlCommand(102, NX_IED_CALL_HARDSTRAP_ASK, 0.0f);

        sleep(2);  // 等待所有处理完成

        printf("\n演示完成\n");
    }

    void Cleanup() {
        if(m_pObserver) {
            delete m_pObserver;
            m_pObserver = nullptr;
        }

        if(m_pSubject) {
            delete m_pSubject;
            m_pSubject = nullptr;
        }
    }
};

// 主函数示例
int main() {
    MessageSystem system;

    if(!system.Initialize()) {
        printf("系统初始化失败\n");
        return -1;
    }

    system.RunDemo();

    return 0;
}

## 8. 扩展机制

### 8.1 添加新的观察者

#### 8.1.1 自定义观察者ID

系统为用户自定义观察者预留了ID范围：

```cpp
// 用户自定义观察者ID起始编号
const int USER_DEF_OBSERVER_ID_START_NO = 1000000;

// 自定义观察者ID示例
const int MY_CUSTOM_OBSERVER_ID = (USER_DEF_OBSERVER_ID_START_NO + 100);
const int DATA_LOGGER_OBSERVER_ID = (USER_DEF_OBSERVER_ID_START_NO + 101);
const int ALARM_PROCESSOR_OBSERVER_ID = (USER_DEF_OBSERVER_ID_START_NO + 102);
```

#### 8.1.2 特化观察者实现

```cpp
class DataLoggerObserver : public CNXObserver {
private:
    ofstream m_logFile;

public:
    DataLoggerObserver() {
        // 打开日志文件
        m_logFile.open("event_log.txt", ios::app);
    }

    ~DataLoggerObserver() {
        if(m_logFile.is_open()) {
            m_logFile.close();
        }
    }

    bool Initialize() {
        // 设置观察者信息
        MEDIATOR_REG_OBJ_INFO regInfo;
        regInfo.nObjID = DATA_LOGGER_OBSERVER_ID;
        strcpy(regInfo.cObjDesc, "数据日志记录观察者");
        regInfo.pRegObj = this;
        SetRegObjInfo(regInfo);

        // 只关注事件消息，不关注命令结果
        EC_INFO_ORDER_LIST eventTypes;
        eventTypes.push_back(NX_IED_EVENT_FILE_REPORT);
        eventTypes.push_back(NX_IED_ALARM_FILE_REPORT);
        SetCareEventType(true, eventTypes);

        // 关注所有设备
        EC_DEV_ORDER_LIST devices;
        devices.push_back({ALL_DEV, DEV_CATEGORY_IED});
        SetCareDev(true, devices);

        // 设置事件回调
        SetEventCallBack(this, OnEventForLogging);

        return CNXObserver::Init();
    }

    static int OnEventForLogging(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        DataLoggerObserver* pThis = (DataLoggerObserver*)pObj;
        return pThis->LogEvent(eventMsg);
    }

private:
    int LogEvent(NX_EVENT_MESSAGE &eventMsg) {
        if(!m_logFile.is_open()) return -1;

        // 格式化日志记录
        time_t rawTime = eventMsg.n_send_utctm;
        struct tm* timeInfo = localtime(&rawTime);
        char timeStr[64];
        strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", timeInfo);

        m_logFile << "[" << timeStr << "] "
                  << "设备ID:" << eventMsg.n_event_obj << " "
                  << "事件类型:" << eventMsg.n_msg_type << " "
                  << "源:" << eventMsg.c_src_name << endl;

        m_logFile.flush();  // 立即写入文件

        return 0;
    }
};
```

### 8.2 添加新的主题

#### 8.2.1 自定义主题ID

```cpp
// 用户自定义主题ID起始编号
const int USER_DEF_SUBJECT_ID_START_NO = 2000000;

// 自定义主题ID示例
const int DEVICE_MANAGER_SUBJECT_ID = (USER_DEF_SUBJECT_ID_START_NO + 100);
const int ALARM_PROCESSOR_SUBJECT_ID = (USER_DEF_SUBJECT_ID_START_NO + 101);
const int DATA_COLLECTOR_SUBJECT_ID = (USER_DEF_SUBJECT_ID_START_NO + 102);
```

#### 8.2.2 特化主题实现

```cpp
class DeviceManagerSubject : public CNXSubject {
private:
    map<int, DeviceInfo> m_deviceMap;  // 设备信息映射

    struct DeviceInfo {
        int deviceId;
        string deviceName;
        int status;  // 0-离线, 1-在线, 2-故障
        time_t lastUpdateTime;
    };

public:
    bool Initialize() {
        // 设置主题信息
        MEDIATOR_REG_OBJ_INFO regInfo;
        regInfo.nObjID = DEVICE_MANAGER_SUBJECT_ID;
        strcpy(regInfo.cObjDesc, "设备管理主题");
        regInfo.pRegObj = this;
        SetRegObjInfo(regInfo);

        // 设置管理的设备范围
        EC_DEV_ORDER_LIST managedDevices;
        for(int i = 100; i <= 199; i++) {  // 管理设备100-199
            managedDevices.push_back({i, DEV_CATEGORY_IED});
        }
        SetManagerDev(true, managedDevices);

        // 设置命令回调
        SetCmdCallBack(this, OnDeviceCommand);

        // 初始化设备信息
        InitializeDeviceInfo();

        return CNXSubject::Init();
    }

    static int OnDeviceCommand(LPVOID pObj, NX_COMMON_MESSAGE &cmdMsg) {
        DeviceManagerSubject* pThis = (DeviceManagerSubject*)pObj;
        return pThis->HandleDeviceCommand(cmdMsg);
    }

private:
    void InitializeDeviceInfo() {
        // 初始化设备信息
        for(int i = 100; i <= 199; i++) {
            DeviceInfo info;
            info.deviceId = i;
            info.deviceName = "IED_" + to_string(i);
            info.status = 1;  // 默认在线
            info.lastUpdateTime = time(NULL);
            m_deviceMap[i] = info;
        }
    }

    int HandleDeviceCommand(NX_COMMON_MESSAGE &cmdMsg) {
        printf("设备管理主题收到命令：设备ID=%d, 命令类型=%d\n",
               cmdMsg.n_obj_id, cmdMsg.n_msg_type);

        bool success = false;

        switch(cmdMsg.n_msg_type) {
            case NX_IED_CALL_DEVICE_STATUS_ASK:  // 查询设备状态
                success = HandleDeviceStatusQuery(cmdMsg);
                break;
            case NX_IED_CTRL_DEVICE_RESET_ASK:   // 设备复位
                success = HandleDeviceReset(cmdMsg);
                break;
            case NX_IED_CTRL_DEVICE_CONFIG_ASK:  // 设备配置
                success = HandleDeviceConfig(cmdMsg);
                break;
            default:
                printf("不支持的设备管理命令：%d\n", cmdMsg.n_msg_type);
                break;
        }

        // 发送处理结果
        SendDeviceCommandResult(cmdMsg, success);

        return 0;
    }

    bool HandleDeviceStatusQuery(NX_COMMON_MESSAGE &cmdMsg) {
        auto it = m_deviceMap.find(cmdMsg.n_obj_id);
        if(it == m_deviceMap.end()) {
            return false;  // 设备不存在
        }

        // 更新设备状态（模拟）
        it->second.lastUpdateTime = time(NULL);

        // 发送设备状态事件
        SendDeviceStatusEvent(it->second);

        return true;
    }

    bool HandleDeviceReset(NX_COMMON_MESSAGE &cmdMsg) {
        auto it = m_deviceMap.find(cmdMsg.n_obj_id);
        if(it == m_deviceMap.end()) {
            return false;
        }

        printf("执行设备复位：%s\n", it->second.deviceName.c_str());

        // 模拟复位过程
        it->second.status = 0;  // 先设为离线
        sleep(1);               // 模拟复位时间
        it->second.status = 1;  // 复位后在线
        it->second.lastUpdateTime = time(NULL);

        // 发送复位完成事件
        SendDeviceResetEvent(it->second);

        return true;
    }

    bool HandleDeviceConfig(NX_COMMON_MESSAGE &cmdMsg) {
        auto it = m_deviceMap.find(cmdMsg.n_obj_id);
        if(it == m_deviceMap.end()) {
            return false;
        }

        printf("配置设备：%s\n", it->second.deviceName.c_str());

        // 模拟配置过程
        it->second.lastUpdateTime = time(NULL);

        // 发送配置完成事件
        SendDeviceConfigEvent(it->second);

        return true;
    }

    void SendDeviceCommandResult(NX_COMMON_MESSAGE &originalCmd, bool success) {
        NX_COMMON_MESSAGE resultMsg = originalCmd;
        resultMsg.n_msg_type = GetDeviceResultMsgType(originalCmd.n_msg_type);
        resultMsg.n_result = success ? 0 : 1;
        resultMsg.n_send_utctm = time(NULL);

        SendResult(resultMsg);
    }

    void SendDeviceStatusEvent(const DeviceInfo &deviceInfo) {
        NX_EVENT_MESSAGE eventMsg;
        memset(&eventMsg, 0, sizeof(eventMsg));

        strcpy(eventMsg.c_src_name, "DeviceManager");
        strcpy(eventMsg.c_dst_name, "broadcast");
        eventMsg.n_send_utctm = time(NULL);
        eventMsg.n_msg_topic = NX_TOPIC_EVENT;
        eventMsg.n_msg_type = NX_IED_EVENT_DEVICE_STATUS_REPORT;
        eventMsg.n_event_obj = deviceInfo.deviceId;
        eventMsg.n_event_obj_type = DEV_CATEGORY_IED;

        // 添加状态信息
        NX_EVENT_FIELD_STRUCT statusField;
        statusField.n_field_type = NX_FIELD_TYPE_DEVICE_STATUS;
        statusField.n_value = deviceInfo.status;
        eventMsg.list_subfields.push_back(statusField);

        SendEventNotify(eventMsg);
        eventMsg.list_subfields.clear();
    }

    void SendDeviceResetEvent(const DeviceInfo &deviceInfo) {
        NX_EVENT_MESSAGE eventMsg;
        memset(&eventMsg, 0, sizeof(eventMsg));

        strcpy(eventMsg.c_src_name, "DeviceManager");
        eventMsg.n_msg_type = NX_IED_EVENT_DEVICE_RESET_REPORT;
        eventMsg.n_event_obj = deviceInfo.deviceId;
        eventMsg.n_send_utctm = time(NULL);

        SendEventNotify(eventMsg);
    }

    void SendDeviceConfigEvent(const DeviceInfo &deviceInfo) {
        NX_EVENT_MESSAGE eventMsg;
        memset(&eventMsg, 0, sizeof(eventMsg));

        strcpy(eventMsg.c_src_name, "DeviceManager");
        eventMsg.n_msg_type = NX_IED_EVENT_DEVICE_CONFIG_REPORT;
        eventMsg.n_event_obj = deviceInfo.deviceId;
        eventMsg.n_send_utctm = time(NULL);

        SendEventNotify(eventMsg);
    }

    int GetDeviceResultMsgType(int cmdMsgType) {
        switch(cmdMsgType) {
            case NX_IED_CALL_DEVICE_STATUS_ASK:
                return NX_IED_CALL_DEVICE_STATUS_REP;
            case NX_IED_CTRL_DEVICE_RESET_ASK:
                return NX_IED_CTRL_DEVICE_RESET_REP;
            case NX_IED_CTRL_DEVICE_CONFIG_ASK:
                return NX_IED_CTRL_DEVICE_CONFIG_REP;
            default:
                return cmdMsgType + 1000;
        }
    }
};
```

### 8.3 添加新的消息类型

#### 8.3.1 定义新的消息类型常量

```cpp
// 自定义事件消息类型
const int NX_IED_EVENT_DEVICE_STATUS_REPORT = 50001;    // 设备状态报告
const int NX_IED_EVENT_DEVICE_RESET_REPORT = 50002;     // 设备复位报告
const int NX_IED_EVENT_DEVICE_CONFIG_REPORT = 50003;    // 设备配置报告

// 自定义命令消息类型
const int NX_IED_CALL_DEVICE_STATUS_ASK = 60001;        // 查询设备状态
const int NX_IED_CTRL_DEVICE_RESET_ASK = 60002;         // 设备复位命令
const int NX_IED_CTRL_DEVICE_CONFIG_ASK = 60003;        // 设备配置命令

// 对应的回复消息类型
const int NX_IED_CALL_DEVICE_STATUS_REP = 60101;        // 设备状态查询回复
const int NX_IED_CTRL_DEVICE_RESET_REP = 60102;         // 设备复位回复
const int NX_IED_CTRL_DEVICE_CONFIG_REP = 60103;        // 设备配置回复
```

#### 8.3.2 扩展消息字段类型

```cpp
// 自定义字段类型
const int NX_FIELD_TYPE_DEVICE_STATUS = 70001;          // 设备状态字段
const int NX_FIELD_TYPE_CONFIG_PARAM = 70002;           // 配置参数字段
const int NX_FIELD_TYPE_RESET_TYPE = 70003;             // 复位类型字段
```

### 8.4 自定义回调扩展

#### 8.4.1 多级回调处理器

```cpp
class MultiLevelCallbackHandler {
private:
    struct CallbackLevel {
        int priority;
        LPVOID pObj;
        PFUNC_ON_NXEVENT_MSG_HANDLE handler;
        string description;
    };

    vector<CallbackLevel> m_eventHandlers;

public:
    void AddEventHandler(int priority, LPVOID pObj,
                        PFUNC_ON_NXEVENT_MSG_HANDLE handler,
                        const string& desc) {
        CallbackLevel level;
        level.priority = priority;
        level.pObj = pObj;
        level.handler = handler;
        level.description = desc;

        m_eventHandlers.push_back(level);

        // 按优先级排序（高优先级在前）
        sort(m_eventHandlers.begin(), m_eventHandlers.end(),
             [](const CallbackLevel& a, const CallbackLevel& b) {
                 return a.priority > b.priority;
             });
    }

    static int ProcessEventWithLevels(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        MultiLevelCallbackHandler* pHandler = (MultiLevelCallbackHandler*)pObj;

        for(const auto& level : pHandler->m_eventHandlers) {
            printf("执行回调：%s (优先级:%d)\n",
                   level.description.c_str(), level.priority);

            int result = level.handler(level.pObj, eventMsg);
            if(result != 0) {
                printf("回调处理失败，中断处理链\n");
                return result;
            }
        }

        return 0;
    }
};
```

#### 8.4.2 条件回调过滤器

```cpp
class ConditionalCallbackFilter {
private:
    LPVOID m_pOriginalObj;
    PFUNC_ON_NXEVENT_MSG_HANDLE m_pOriginalHandler;
    function<bool(const NX_EVENT_MESSAGE&)> m_filterFunc;

public:
    ConditionalCallbackFilter(LPVOID pObj, PFUNC_ON_NXEVENT_MSG_HANDLE handler,
                             function<bool(const NX_EVENT_MESSAGE&)> filter)
        : m_pOriginalObj(pObj), m_pOriginalHandler(handler), m_filterFunc(filter) {}

    static int FilteredEventHandler(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        ConditionalCallbackFilter* pFilter = (ConditionalCallbackFilter*)pObj;

        // 应用过滤条件
        if(!pFilter->m_filterFunc(eventMsg)) {
            printf("事件被过滤器拒绝，跳过处理\n");
            return 0;
        }

        // 调用原始处理器
        return pFilter->m_pOriginalHandler(pFilter->m_pOriginalObj, eventMsg);
    }
};

// 使用示例
void SetupFilteredCallback(CNXObserver* pObserver, LPVOID pObj,
                          PFUNC_ON_NXEVENT_MSG_HANDLE handler) {
    // 创建过滤器：只处理告警级别的事件
    auto filter = [](const NX_EVENT_MESSAGE& msg) -> bool {
        return msg.n_msg_type == NX_IED_ALARM_FILE_REPORT;
    };

    ConditionalCallbackFilter* pFilter =
        new ConditionalCallbackFilter(pObj, handler, filter);

    pObserver->SetEventCallBack(pFilter,
                               ConditionalCallbackFilter::FilteredEventHandler);
}

## 9. 注意事项

### 9.1 开发注意事项

#### 9.1.1 ID管理
```cpp
// ✅ 正确：使用预定义的ID范围
const int MY_OBSERVER_ID = USER_DEF_OBSERVER_ID_START_NO + 1;
const int MY_SUBJECT_ID = USER_DEF_SUBJECT_ID_START_NO + 1;

// ❌ 错误：使用系统保留的ID范围
const int MY_OBSERVER_ID = 100;  // 可能与系统ID冲突
```

#### 9.1.2 回调函数安全
```cpp
// ✅ 正确：检查回调参数有效性
static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
    if(pObj == NULL) {
        printf("回调对象指针为空\n");
        return -1;
    }

    MyClass* pThis = (MyClass*)pObj;
    return pThis->HandleEvent(eventMsg);
}

// ❌ 错误：不检查参数直接使用
static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
    MyClass* pThis = (MyClass*)pObj;  // 可能导致崩溃
    return pThis->HandleEvent(eventMsg);
}
```

#### 9.1.3 消息生命周期管理
```cpp
// ✅ 正确：及时清理消息中的动态内容
void SendMessage() {
    NX_COMMON_MESSAGE msg;
    // ... 填充消息

    // 添加字段
    NX_COMMON_FIELD_STRUCT field;
    msg.list_subfields.push_back(field);

    // 发送消息
    pObserver->SendCommand(msg);

    // 清理资源
    msg.list_subfields.clear();  // 必须清理
}

// ❌ 错误：忘记清理导致内存泄漏
void SendMessage() {
    NX_COMMON_MESSAGE msg;
    msg.list_subfields.push_back(field);
    pObserver->SendCommand(msg);
    // 忘记清理 msg.list_subfields
}
```

#### 9.1.4 线程安全考虑
```cpp
// ✅ 正确：在多线程环境中保护共享资源
class ThreadSafeObserver : public CNXObserver {
private:
    mutex m_dataMutex;
    map<int, string> m_sharedData;

public:
    static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        ThreadSafeObserver* pThis = (ThreadSafeObserver*)pObj;

        // 使用锁保护共享数据
        lock_guard<mutex> lock(pThis->m_dataMutex);
        pThis->m_sharedData[eventMsg.n_event_obj] = "processed";

        return 0;
    }
};
```

### 9.2 性能优化注意事项

#### 9.2.1 避免频繁的动态内存分配
```cpp
// ✅ 正确：重用消息对象
class MessagePool {
private:
    queue<NX_COMMON_MESSAGE*> m_messagePool;
    mutex m_poolMutex;

public:
    NX_COMMON_MESSAGE* GetMessage() {
        lock_guard<mutex> lock(m_poolMutex);
        if(m_messagePool.empty()) {
            return new NX_COMMON_MESSAGE();
        }

        NX_COMMON_MESSAGE* msg = m_messagePool.front();
        m_messagePool.pop();
        memset(msg, 0, sizeof(NX_COMMON_MESSAGE));
        return msg;
    }

    void ReturnMessage(NX_COMMON_MESSAGE* msg) {
        if(msg == NULL) return;

        msg->list_subfields.clear();  // 清理内容

        lock_guard<mutex> lock(m_poolMutex);
        m_messagePool.push(msg);
    }
};
```

#### 9.2.2 批量处理优化
```cpp
// ✅ 正确：批量处理事件
class BatchEventProcessor {
private:
    vector<NX_EVENT_MESSAGE> m_eventBatch;
    const size_t BATCH_SIZE = 100;

public:
    static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        BatchEventProcessor* pThis = (BatchEventProcessor*)pObj;

        pThis->m_eventBatch.push_back(eventMsg);

        if(pThis->m_eventBatch.size() >= pThis->BATCH_SIZE) {
            pThis->ProcessBatch();
        }

        return 0;
    }

private:
    void ProcessBatch() {
        // 批量处理事件
        for(const auto& event : m_eventBatch) {
            ProcessSingleEvent(event);
        }

        m_eventBatch.clear();
    }
};
```

### 9.3 错误处理注意事项

#### 9.3.1 完整的错误处理
```cpp
// ✅ 正确：完整的错误处理流程
bool InitializeSystem() {
    // 1. 初始化服务中介者
    if(!InitSrvMed()) {
        printf("初始化服务中介者失败\n");
        return false;
    }

    // 2. 创建观察者
    CNXObserver* pObserver = new CNXObserver();
    if(pObserver == NULL) {
        printf("创建观察者失败\n");
        return false;
    }

    // 3. 初始化观察者
    if(!pObserver->Init()) {
        printf("初始化观察者失败\n");
        delete pObserver;  // 清理资源
        return false;
    }

    return true;
}
```

#### 9.3.2 回调函数中的异常处理
```cpp
// ✅ 正确：在回调中捕获异常
static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
    try {
        if(pObj == NULL) return -1;

        MyClass* pThis = (MyClass*)pObj;
        return pThis->HandleEvent(eventMsg);
    }
    catch(const exception& e) {
        printf("事件处理异常：%s\n", e.what());
        return -1;
    }
    catch(...) {
        printf("事件处理发生未知异常\n");
        return -1;
    }
}
```

### 9.4 调试和故障排查

#### 9.4.1 日志记录最佳实践
```cpp
// ✅ 正确：详细的日志记录
class LoggingObserver : public CNXObserver {
public:
    static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
        LoggingObserver* pThis = (LoggingObserver*)pObj;

        // 记录接收到的事件
        printf("[%u] 收到事件：设备ID=%d, 类型=%d, 源=%s\n",
               (unsigned int)time(NULL), eventMsg.n_event_obj,
               eventMsg.n_msg_type, eventMsg.c_src_name);

        int result = pThis->ProcessEvent(eventMsg);

        // 记录处理结果
        printf("[%u] 事件处理%s\n",
               (unsigned int)time(NULL),
               (result == 0) ? "成功" : "失败");

        return result;
    }
};
```

#### 9.4.2 常见问题排查

**问题1：观察者收不到事件**
- 检查观察者是否正确注册到中介者
- 验证事件类型和设备ID的关注设置
- 确认主题是否正确发送事件

**问题2：命令发送失败**
- 检查目标设备是否有对应的主题管理
- 验证设备ID和设备类型是否正确
- 确认中介者的设备映射是否建立

**问题3：回调函数不被调用**
- 检查回调函数是否正确设置
- 验证回调对象指针是否有效
- 确认消息类型是否匹配

#### 9.4.3 调试工具函数
```cpp
// 调试辅助函数
void DumpEventMessage(const NX_EVENT_MESSAGE& msg) {
    printf("=== 事件消息详情 ===\n");
    printf("源：%s\n", msg.c_src_name);
    printf("目标：%s\n", msg.c_dst_name);
    printf("时间：%u\n", msg.n_send_utctm);
    printf("主题：%u\n", msg.n_msg_topic);
    printf("类型：%u\n", msg.n_msg_type);
    printf("事件对象：%d\n", msg.n_event_obj);
    printf("对象类型：%d\n", msg.n_event_obj_type);
    printf("字段数量：%zu\n", msg.list_subfields.size());
    printf("==================\n");
}

void DumpCommonMessage(const NX_COMMON_MESSAGE& msg) {
    printf("=== 通用消息详情 ===\n");
    printf("源：%s\n", msg.c_src_name);
    printf("目标：%s\n", msg.c_dst_name);
    printf("时间：%u\n", msg.n_send_utctm);
    printf("主题：%u\n", msg.n_msg_topic);
    printf("类型：%u\n", msg.n_msg_type);
    printf("对象ID：%d\n", msg.n_obj_id);
    printf("对象类型：%d\n", msg.n_obj_type);
    printf("结果：%d\n", msg.n_result);
    printf("字段数量：%zu\n", msg.list_subfields.size());
    printf("==================\n");
}
```

### 9.5 维护注意事项

#### 9.5.1 版本兼容性
- 新增消息类型时使用新的ID范围，避免与现有类型冲突
- 修改消息结构时保持向后兼容性
- 更新接口时提供版本检查机制

#### 9.5.2 配置管理
```cpp
// ✅ 正确：使用配置文件管理ID和参数
class ConfigManager {
private:
    map<string, int> m_idMap;

public:
    bool LoadConfig(const string& configFile) {
        // 从配置文件加载ID映射
        ifstream file(configFile);
        string line;

        while(getline(file, line)) {
            size_t pos = line.find('=');
            if(pos != string::npos) {
                string key = line.substr(0, pos);
                int value = stoi(line.substr(pos + 1));
                m_idMap[key] = value;
            }
        }

        return true;
    }

    int GetObserverId(const string& name) {
        auto it = m_idMap.find(name + "_OBSERVER_ID");
        return (it != m_idMap.end()) ? it->second : USER_DEF_INVALID_ID;
    }

    int GetSubjectId(const string& name) {
        auto it = m_idMap.find(name + "_SUBJECT_ID");
        return (it != m_idMap.end()) ? it->second : USER_DEF_INVALID_ID;
    }
};
```

#### 9.5.3 资源清理
```cpp
// ✅ 正确：完整的资源清理
class SystemManager {
private:
    vector<CNXObserver*> m_observers;
    vector<CNXSubject*> m_subjects;

public:
    ~SystemManager() {
        Cleanup();
    }

    void Cleanup() {
        // 清理观察者
        for(auto pObserver : m_observers) {
            if(pObserver) {
                pObserver->Exit();
                delete pObserver;
            }
        }
        m_observers.clear();

        // 清理主题
        for(auto pSubject : m_subjects) {
            if(pSubject) {
                pSubject->Exit();
                delete pSubject;
            }
        }
        m_subjects.clear();
    }
};

## 10. 总结

### 10.1 模块特点

#### 10.1.1 设计优势

**1. 高度解耦**
- 观察者和主题通过中介者通信，彼此不直接依赖
- 支持动态添加和移除观察者、主题
- 消息路由逻辑集中在中介者中，便于管理和维护

**2. 灵活的订阅机制**
- 支持按事件类型订阅
- 支持按设备ID订阅
- 支持组合条件的复杂订阅

**3. 强大的扩展性**
- 预留了用户自定义ID范围
- 支持自定义消息类型和字段类型
- 提供多种回调扩展机制

**4. 线程安全**
- 中介者提供线程安全的消息分发
- 支持多线程环境下的并发访问
- 回调函数在安全的上下文中执行

**5. 完整的生命周期管理**
- 统一的初始化和清理接口
- 自动的资源管理和释放
- 异常情况下的安全退出

#### 10.1.2 适用场景

**1. 电力系统通信**
- 变电站与主站之间的消息通信
- 设备状态监控和事件上报
- 远程控制命令的下发和执行

**2. 分布式系统**
- 微服务之间的消息传递
- 事件驱动架构的实现
- 系统组件间的解耦通信

**3. 实时监控系统**
- 设备状态实时监控
- 告警信息的及时处理
- 数据采集和分发

### 10.2 最佳实践建议

#### 10.2.1 架构设计建议

**1. 合理划分观察者和主题**
```cpp
// ✅ 推荐：按业务功能划分
class AlarmObserver : public CNXObserver {
    // 专门处理告警相关事件
};

class ControlSubject : public CNXSubject {
    // 专门处理控制相关命令
};

// ❌ 不推荐：功能过于复杂的单一类
class AllInOneObserver : public CNXObserver {
    // 处理所有类型的事件和命令
};
```

**2. 使用配置驱动的设计**
```cpp
// ✅ 推荐：从配置文件读取订阅信息
bool LoadSubscriptionConfig(const string& configFile) {
    // 从配置文件加载事件类型和设备列表
    // 支持运行时动态调整订阅关系
}

// ❌ 不推荐：硬编码订阅关系
void HardcodedSetup() {
    eventTypes.push_back(NX_IED_EVENT_FILE_REPORT);  // 硬编码
    devices.push_back({100, DEV_CATEGORY_IED});       // 硬编码
}
```

#### 10.2.2 性能优化建议

**1. 合理使用消息过滤**
```cpp
// ✅ 推荐：在观察者端进行预过滤
static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
    // 快速过滤不关心的事件
    if(!IsEventOfInterest(eventMsg)) {
        return 0;  // 快速返回
    }

    // 处理感兴趣的事件
    return ProcessEvent(eventMsg);
}
```

**2. 避免在回调中执行耗时操作**
```cpp
// ✅ 推荐：异步处理耗时操作
static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
    MyObserver* pThis = (MyObserver*)pObj;

    // 将事件放入队列，异步处理
    pThis->m_eventQueue.push(eventMsg);
    pThis->m_processingThread.notify();

    return 0;  // 快速返回
}

// ❌ 不推荐：在回调中执行耗时操作
static int OnEventReceived(LPVOID pObj, NX_EVENT_MESSAGE &eventMsg) {
    // 耗时的数据库操作
    SaveEventToDatabase(eventMsg);  // 阻塞其他事件处理

    // 耗时的网络操作
    SendEventToRemoteServer(eventMsg);  // 影响系统性能

    return 0;
}
```

#### 10.2.3 错误处理建议

**1. 分层错误处理**
```cpp
// 应用层错误处理
class ApplicationErrorHandler {
public:
    static int HandleEventError(int errorCode, const NX_EVENT_MESSAGE& msg) {
        switch(errorCode) {
            case EVENT_PARSE_ERROR:
                LogError("事件解析失败", msg);
                return RETRY_LATER;
            case EVENT_PROCESS_ERROR:
                LogError("事件处理失败", msg);
                return IGNORE_EVENT;
            default:
                LogError("未知错误", msg);
                return STOP_PROCESSING;
        }
    }
};

// 系统层错误处理
class SystemErrorHandler {
public:
    static void HandleSystemError(const string& component, const string& error) {
        // 记录系统级错误
        // 可能需要重启组件或整个系统
    }
};
```

**2. 优雅降级机制**
```cpp
class GracefulDegradation {
private:
    bool m_isNormalMode;

public:
    int ProcessEvent(NX_EVENT_MESSAGE &eventMsg) {
        if(m_isNormalMode) {
            // 正常模式：完整处理
            return FullProcessing(eventMsg);
        } else {
            // 降级模式：简化处理
            return SimplifiedProcessing(eventMsg);
        }
    }

    void SwitchToSafeMode() {
        m_isNormalMode = false;
        printf("切换到安全模式\n");
    }
};
```

#### 10.2.4 测试建议

**1. 单元测试**
```cpp
class ObserverTest {
public:
    void TestEventHandling() {
        // 创建测试观察者
        TestObserver observer;
        observer.Initialize();

        // 创建测试事件
        NX_EVENT_MESSAGE testEvent;
        FillTestEvent(testEvent);

        // 测试事件处理
        int result = observer.HandleEvent(testEvent);
        assert(result == 0);

        // 验证处理结果
        assert(observer.GetProcessedEventCount() == 1);
    }
};
```

**2. 集成测试**
```cpp
class IntegrationTest {
public:
    void TestObserverSubjectCommunication() {
        // 初始化系统
        InitializeTestSystem();

        // 创建观察者和主题
        TestObserver observer;
        TestSubject subject;

        // 发送测试命令
        NX_COMMON_MESSAGE testCmd;
        observer.SendCommand(testCmd);

        // 等待处理完成
        WaitForProcessing();

        // 验证结果
        assert(subject.GetReceivedCommandCount() == 1);
        assert(observer.GetReceivedResultCount() == 1);
    }
};
```

### 10.3 发展方向

#### 10.3.1 技术发展方向

**1. 云原生支持**
- 支持容器化部署
- 集成服务发现机制
- 支持水平扩展

**2. 微服务架构**
- 将中介者拆分为独立的微服务
- 支持跨进程、跨主机的消息通信
- 集成消息队列中间件

**3. 智能化增强**
- 集成机器学习算法进行事件预测
- 智能的消息路由和负载均衡
- 自适应的性能优化

#### 10.3.2 功能增强方向

**1. 更丰富的订阅模式**
- 支持基于内容的订阅
- 支持时间窗口的订阅
- 支持复杂条件的组合订阅

**2. 增强的可靠性**
- 消息持久化机制
- 故障自动恢复
- 消息重传和去重

**3. 更好的监控和诊断**
- 实时性能监控
- 消息流量分析
- 自动故障诊断

### 10.4 学习路径建议

#### 10.4.1 基础学习
1. **理解设计模式**：深入学习观察者模式和中介者模式
2. **掌握C++基础**：熟练掌握C++的面向对象编程
3. **了解多线程编程**：理解线程安全和同步机制

#### 10.4.2 进阶学习
1. **系统架构设计**：学习分布式系统和消息中间件
2. **性能优化技术**：掌握高性能编程技巧
3. **电力系统知识**：了解电力系统的业务需求

#### 10.4.3 实践建议
1. **从简单示例开始**：先实现基本的观察者-主题通信
2. **逐步增加复杂性**：添加过滤、路由等高级功能
3. **关注实际应用**：结合具体的业务场景进行开发

通过深入学习和实践这个观察者-中介者-主题模式架构，可以掌握现代软件系统中消息通信和事件处理的核心技术，为开发高质量的分布式系统奠定坚实基础。
```
```
```
```
```
```