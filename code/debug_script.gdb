# GDB调试脚本 - 用于调试__RecordCtrlLogToDb函数崩溃问题
# 使用方法: gdb -x debug_script.gdb your_program

# 设置环境
set environment LD_LIBRARY_PATH=/path/to/your/lib
set print pretty on
set print object on
set pagination off
set confirm off

# 设置断点
break CNXEcSrvProOperation::__RecordCtrlLogToDb
break NXEcSrvProOperation.cpp:1438
break NXEcSrvProOperation.cpp:1447
break NXEcSrvProOperation.cpp:1459
break NXEcSrvProOperation.cpp:1466
break NXEcSrvProOperation.cpp:1479
break NXEcSrvProOperation.cpp:1488
break NXEcSrvProOperation.cpp:1578
break NXEcSrvProOperation.cpp:1591

# 定义自定义检查命令
define check_basic_pointers
    printf "\n=== 基本指针检查 ===\n"
    printf "this指针: %p\n", this
    if this != 0
        printf "m_pModelSeekIns: %p\n", this->m_pModelSeekIns
        printf "m_pProParam: %p\n", this->m_pProParam
    else
        printf "ERROR: this指针为空!\n"
    end
    printf "pNxCmdList: %p\n", pNxCmdList
    if pNxCmdList != 0
        printf "pNxCmdList->size(): %zu\n", pNxCmdList->size()
        printf "pNxCmdList->empty(): %s\n", pNxCmdList->empty() ? "true" : "false"
    else
        printf "ERROR: pNxCmdList为空!\n"
    end
    printf "========================\n\n"
end

define check_iterator_state
    printf "\n=== 迭代器状态检查 ===\n"
    printf "nCmdIndex: %d\n", nCmdIndex
    printf "iteCmd地址: %p\n", &iteCmd
    if pNxCmdList != 0
        printf "容器大小: %zu\n", pNxCmdList->size()
        printf "是否到达end(): %s\n", (iteCmd == pNxCmdList->end()) ? "true" : "false"
    end
    printf "========================\n\n"
end

define check_message_data
    printf "\n=== 消息数据检查 ===\n"
    printf "TmpNxCmd地址: %p\n", &TmpNxCmd
    printf "TmpNxCmd.n_obj_id: %d\n", TmpNxCmd.n_obj_id
    printf "TmpNxCmd.n_msg_type: %d\n", TmpNxCmd.n_msg_type
    printf "TmpNxCmd.list_subfields.size(): %zu\n", TmpNxCmd.list_subfields.size()
    printf "========================\n\n"
end

define check_ied_config
    printf "\n=== IED配置检查 ===\n"
    printf "pEcIedTb: %p\n", pEcIedTb
    if pEcIedTb != 0
        printf "pEcIedTb->pPrimDevCfg: %p\n", pEcIedTb->pPrimDevCfg
        printf "pEcIedTb->pIed: %p\n", pEcIedTb->pIed
    else
        printf "ERROR: pEcIedTb为空!\n"
    end
    printf "========================\n\n"
end

define check_memory_layout
    printf "\n=== 内存布局检查 ===\n"
    printf "NX_COMMON_MESSAGE大小: %zu\n", sizeof(NX_COMMON_MESSAGE)
    printf "CTRL_INFO_RECORD大小: %zu\n", sizeof(CTRL_INFO_RECORD)
    printf "指针大小: %zu\n", sizeof(void*)
    printf "this内存对齐: %zu\n", (unsigned long)this % sizeof(void*)
    printf "========================\n\n"
end

define dump_stack_info
    printf "\n=== 栈信息转储 ===\n"
    printf "栈指针: %p\n", $rsp
    printf "基址指针: %p\n", $rbp
    printf "栈内容 (前10个字):\n"
    x/10x $rsp
    printf "========================\n\n"
end

define full_crash_analysis
    printf "\n\n*** 崩溃分析开始 ***\n"
    printf "程序崩溃时间: "
    shell date
    printf "\n"
    
    printf "=== 调用栈 ===\n"
    bt
    printf "\n"
    
    printf "=== 寄存器状态 ===\n"
    info registers
    printf "\n"
    
    check_basic_pointers
    dump_stack_info
    
    printf "=== 当前汇编代码 ===\n"
    disas
    printf "\n"
    
    printf "*** 崩溃分析结束 ***\n\n"
end

# 设置断点命令
commands 1
    printf "\n>>> 进入__RecordCtrlLogToDb函数\n"
    check_basic_pointers
    check_memory_layout
    continue
end

commands 2
    printf "\n>>> 迭代器初始化完成\n"
    check_iterator_state
    continue
end

commands 3
    printf "\n>>> 准备访问迭代器内容\n"
    check_iterator_state
    continue
end

commands 4
    printf "\n>>> 成功复制消息内容\n"
    check_message_data
    continue
end

commands 5
    printf "\n>>> 准备获取IED配置\n"
    check_basic_pointers
    continue
end

commands 6
    printf "\n>>> 检查主设备配置指针\n"
    check_ied_config
    continue
end

commands 7
    printf "\n>>> 检查设备指针\n"
    check_ied_config
    continue
end

commands 8
    printf "\n>>> 准备调用__CvtNxMsgToLogStruct\n"
    check_message_data
    check_ied_config
    continue
end

commands 9
    printf "\n>>> 准备调用__WriteLogToDb\n"
    printf "CtrlInfoSet地址: %p\n", &CtrlInfoSet
    continue
end

# 设置信号处理
handle SIGSEGV stop print
handle SIGABRT stop print
handle SIGFPE stop print

# 当程序收到信号时执行完整分析
define hook-stop
    if $_siginfo
        printf "\n!!! 程序收到信号: %d !!!\n", $_siginfo.si_signo
        if $_siginfo.si_signo == 11
            printf "段错误 (SIGSEGV) 检测到!\n"
            full_crash_analysis
        end
        if $_siginfo.si_signo == 6
            printf "异常终止 (SIGABRT) 检测到!\n"
            full_crash_analysis
        end
    end
end

# 启动程序
printf "开始调试__RecordCtrlLogToDb函数...\n"
printf "设置了以下断点:\n"
printf "1. 函数入口\n"
printf "2. 迭代器初始化\n"
printf "3. 迭代器访问\n"
printf "4. 消息复制\n"
printf "5. IED配置获取\n"
printf "6. 主设备配置检查\n"
printf "7. 设备指针检查\n"
printf "8. 消息转换函数调用\n"
printf "9. 数据库写入函数调用\n"
printf "\n程序将在断点处停止并显示调试信息...\n\n"

run
