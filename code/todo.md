TNXEcProAsdu101GWS
    该类需要新增处理SettingUp关键字，响应主站召唤定值文件列表；
    当识别到SettingUp关键字后，从数据库指定的根路径下找到Setting文件夹，并找到对应的设备文件夹，根据时间过滤，获取文件列表信息。

    修改函数涉及：DirectResFromLocal

    virtual int ___QueryGeneralFilesList_SettingUp(IN ASDU_TIME AsduTime,IN const char * cFindFileName,OUT FILE_PROPERTY_INF_LIST & GeneralFileList);
    获取定值文件文件夹下的对应时间范围文件,组成列表返回
    ASDU_TIME AsduTime:时间范围
    const char * cFindFileName:文件名
    FILE_PROPERTY_INF_LIST & GeneralFileList:文件信息列表
    0：成功，-1：失败

TNXEcProAsdu103GWS
    该类需要新增处理SettingUp关键字，响应主站召唤定值文件。

    修改函数涉及：DirectResFromLocal；

    virtual int _GeneralFileHandle_SettingUp(IN const char * cFileName,IN int nBeginSendPos,OUT PRO_FRAME_BODY_LIST & lResult,IN string & strFullFname);
    定值文件上送处理函数
    const char * cFileName:指定的文件名称
    int nBeginSendPos:起始传输位置
    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
    0：成功，-1：失败
    
TNXEcProAsdu106GWS
    virtual int ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg,OUT PRO_FRAME_BODY_LIST & lBody) ;
    根据NX事件信息生成规约事件列表
    NX_EVENT_MESSAGE * pMsg :事件信结构指针
    PRO_FRAME_BODY_LIST  & lBody :规约信息体
    int 0-成功 其它失败
    0：成功，-1：失败

    virtual int FormatAsdu106Body(IN ASDU106_INFO &Asdu106Info,OUT PRO_FRAME_BODY_LIST  & lBody,IN int nReserve = 0);
    根据ASDU106信息结构格式化ASDU106报文体
    ASDU106_INFO &Asdu106Info:  asdu106信息结构体
    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
    int nReserve:备用
    0：成功，-1：失败




填写说明 [~]:使用 [-]不使用[/]可选[*]必须填写[*/]自动
基本结构属性名                                用途说明        填写要求
char c_src_name[64]                          [√]消息发送者   [*/]消息总线自动填写
char c_dst_name [64]                         [√]消息接收者   [*]广播发送,值为:broadcast
unsigned intn send utetm                     [√]消息发送时间 [*/]消息总线自动填写
unsigned int nmsg topic                      [√]消息主题     [*]NX_TOPIC_EVENT
umsign int n_msg_type                        [√]消息类型     [*]NX_IED_EVENT_FILE_REPORT
char c_invoke_id[64]                         [-]             [-]
int n_event_obj                              [√]IED 编号     [*]
int n_data_src                               [-]             [-]
char  c_suffix[128]	                         [√]IED NAME	 [/]61850 方式接入时，填写为该 IED的 name 值
int    n_backup	                             [-]	         [-]     
vector<NX_EVENT_FIELD_STRUCT >list_subfields [√]文件信息      [*]






结果信息与事件信息在观察者-目标者之间的路径
结果信息路径（内部→总线）：
内部 Subject 处理完命令 => Subject.SendResult
服务中介 => Observer.ReplyResult
Observer 回调 NXEcBusSwap::__OnSubjectResultRecv
NXEcBusSwap 调用 m_MbClientLib.SendCommonMsg 发到总线
事件信息路径（内部→总线）：
内部 Subject 产生事件 => Subject.SendEventNotify
服务中介 => Observer.PushEventNotify
Observer 回调 NXEcBusSwap::__OnSubjectEventRecv
NXEcBusSwap 调用 m_MbClientLib.SendEventMsg 发到总线
命令路径（内部 Observer → 总线）
某内部 Observer 发命令给“BUS_SWAP 目标者”：SrvMedLib 路由到 CNXSubject::PushCommand
NXEcBusSwap 在 __InitSubject 中已注册命令回调为 __OnObserverCmdRecv
__OnObserverCmdRecv 将命令入队 m_CommonMsgDeque
后台 __CmdHandleLoop 取出并通过 m_MbClientLib.SendCommonMsg 发到总线




10和101里面有新疆的逻辑，需要去掉