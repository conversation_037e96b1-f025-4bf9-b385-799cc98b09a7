﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{c667aa35-3332-4c38-80f8-f3a7fdd7582a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="MainEntry.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXMainController.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\NXSubject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\NXLoadBusSwapLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\NXECObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\NXLoadSrvMedLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\NXLoadNodeMgrLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\NXLoadNetListenLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\NXLoadEcModelLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\NXEcRegisterObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_common\EcTemplateFunc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ec_srv_modify_note.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\register\Md5.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\register\RegisterKey.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="NXMainController.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\ec_common_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXSubject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXLoadBusSwapLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXECObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXLoadSrvMedLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXLoadNodeMgrLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXLoadNetListenLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXLoadEcModelLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\INXEcModelMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXEcRegisterObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\ec_net_listen_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\EcTemplateFunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\register\Md5.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\register\RegisterKey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_service.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>