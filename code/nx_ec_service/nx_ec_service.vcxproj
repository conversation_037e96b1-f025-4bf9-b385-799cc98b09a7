﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CA01E412-F805-4085-949B-91EBE9A2D1DA}</ProjectGuid>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <Keyword>ManagedCProj</Keyword>
    <RootNamespace>nx_ec_service</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CLRSupport>true</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>../../../../nx_common/;../../../../platform_include/plm_common/;../../../../platform_include/plm_commun/;../../../../platform_include/plm_dbm/;../ec_common/;$(IncludePath)</IncludePath>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;../../../../nx_lib/debug/;</LibraryPath>
    <OutDir>../../../../nx_bin/debug/nx_ec</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>../../../../nx_common/;../../../../platform_include/plm_common/;../../../../platform_include/plm_commun/;../../../../platform_include/plm_dbm/;../ec_common/;$(IncludePath)</IncludePath>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;../../../../nx_lib/release/;</LibraryPath>
    <OutDir>../../../../nx_bin/release/nx_ec</OutDir>
    <EnableManagedIncrementalBuild>true</EnableManagedIncrementalBuild>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;_USE_32BIT_TIME_T</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;_USE_32BIT_TIME_T</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib</AdditionalDependencies>
      <AssemblyDebug>
      </AssemblyDebug>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\..\..\register\Md5.cpp" />
    <ClCompile Include="..\..\..\..\register\RegisterKey.cpp" />
    <ClCompile Include="..\ec_common\EcTemplateFunc.cpp" />
    <ClCompile Include="..\ec_common\NXECObject.cpp" />
    <ClCompile Include="..\ec_common\NXEcRegisterObject.cpp" />
    <ClCompile Include="..\ec_common\NXLoadBusSwapLib.cpp" />
    <ClCompile Include="..\ec_common\NXLoadEcModelLib.cpp" />
    <ClCompile Include="..\ec_common\NXLoadNetListenLib.cpp" />
    <ClCompile Include="..\ec_common\NXLoadNodeMgrLib.cpp" />
    <ClCompile Include="..\ec_common\NXLoadSrvMedLib.cpp" />
    <ClCompile Include="..\ec_common\NXSubject.cpp" />
    <ClCompile Include="ec_srv_modify_note.cpp" />
    <ClCompile Include="MainEntry.cpp" />
    <ClCompile Include="NXMainController.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\register\Md5.h" />
    <ClInclude Include="..\..\..\..\register\RegisterKey.h" />
    <ClInclude Include="..\ec_common\EcTemplateFunc.h" />
    <ClInclude Include="..\ec_common\ec_common_def.h" />
    <ClInclude Include="..\ec_common\ec_net_listen_def.h" />
    <ClInclude Include="..\ec_common\INXEcModelMgr.h" />
    <ClInclude Include="..\ec_common\NXECObject.h" />
    <ClInclude Include="..\ec_common\NXEcRegisterObject.h" />
    <ClInclude Include="..\ec_common\NXLoadBusSwapLib.h" />
    <ClInclude Include="..\ec_common\NXLoadEcModelLib.h" />
    <ClInclude Include="..\ec_common\NXLoadNetListenLib.h" />
    <ClInclude Include="..\ec_common\NXLoadNodeMgrLib.h" />
    <ClInclude Include="..\ec_common\NXLoadSrvMedLib.h" />
    <ClInclude Include="..\ec_common\NXSubject.h" />
    <ClInclude Include="NXMainController.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_service.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>