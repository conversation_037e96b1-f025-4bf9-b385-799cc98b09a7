# ASDU1修改设计方案

## 需求分析

根据福建103规约的新增需求，需要在"定值区变化"时通过ASDU1自发上送，具体要求：

1. **功能类型**: 250 (0xFA)  
2. **信息序号**: 装置地址（n_outaddr103）
3. **DPI值**: 变化后的定值区号
4. **上送时机**: 定值区变化时ASDU1自发上送
5. **事件类型**: `NX_SYS_EVENT_IED_SGZONE_CHG_REPORT` (定值区变化事件)

## 现状分析

### 当前ASDU1处理流程
在 `TNXEcProAsdu1GWS::_CvtNxEventSubFiledToAsdu1Info()` 中：

```cpp
if( nEventType == NX_IED_EVENT_ALARM_REPORT )    // 告警
{
    bGetInfoObj = __DoGetAlarmInfoObj(&(*ite),InfoObj);
}
else if( nEventType == NX_IED_EVENT_HARDTRAP_REPORT ) // 开关量
{
    bGetInfoObj = __DoGetDiInfoObj(&(*ite),InfoObj);
}
else if( nEventType == NX_IED_EVENT_COMMU_REPORT )  // 通信状态
{
    bGetInfoObj = __DoGetCommuStatusInfoObj(Asdu1.Addr,&(*ite),InfoObj);
}
```

### 现有代码结构
- `_CvtNxEventSubFiledToAsdu1Info()`: 核心事件转换方法
- `__DoGetAlarmInfoObj()`, `__DoGetDiInfoObj()`, `__DoGetCommuStatusInfoObj()`: 不同事件类型的InfoObj生成方法
- 目前**缺少对定值区变化事件**的处理分支

### 关键发现
- ✅ **事件路由**: `NX_SYS_EVENT_IED_SGZONE_CHG_REPORT` 事件当前被路由到 **ASDU10** 处理
- ✅ **事件存在**: 定值区变化事件已定义且在系统中使用
- ❌ **处理缺失**: ASDU1中缺少对此事件类型的处理逻辑

## 存在的问题

1. **事件路由问题**: `NX_SYS_EVENT_IED_SGZONE_CHG_REPORT` 当前只路由到ASDU10，未路由到ASDU1
2. **处理逻辑缺失**: ASDU1的 `_CvtNxEventSubFiledToAsdu1Info()` 中缺少定值区事件处理分支
3. **InfoObj生成方法缺失**: 缺少专门的定值区InfoObj生成方法

## 解决方案

### 最小化修改方案

**只修改以下两个文件**：
- `NXEcProAsdu1_GWS.h`
- `NXEcProAsdu1_GWS.cpp`

### 可直接使用的现有代码资源
- ✅ `_CvtNxEventSubFiledToAsdu1Info()` - 事件转换主流程
- ✅ `__DoGetAlarmInfoObj()` 等方法 - InfoObj生成模式参考
- ✅ `NX_EVENT_FIELD_STRUCT` - 事件字段结构体
- ✅ `ASDU_INFO_OBJ` - 信息对象结构体
- ✅ `n_outaddr103` - 103地址字段
- ✅ `n_value` - 事件值字段（定值区号）
- ✅ 现有的错误处理和日志记录模式

### 具体实现

#### 1. 头文件修改 (NXEcProAsdu1_GWS.h)

在 `private` 区域添加新方法声明：

```cpp
/**
* @brief         获得定值区变化事件信息体标识对象信息
* @param[in]     NX_EVENT_FIELD_STRUCT * pField：事件信息子集
* @param[out]    ASDU_INFO_OBJ & InfoObj:信息对象
* @return        bool:true-成功 false-失败
*/
bool __DoGetSetGroupInfoObj(IN NX_EVENT_FIELD_STRUCT * pField, OUT ASDU_INFO_OBJ & InfoObj);
```

#### 2. 源文件修改 (NXEcProAsdu1_GWS.cpp)

**2.1 修改主流程**

在 `_CvtNxEventSubFiledToAsdu1Info()` 方法中添加定值区事件处理分支：

```cpp
if( nEventType == NX_IED_EVENT_ALARM_REPORT )    // 告警
{
    bGetInfoObj = __DoGetAlarmInfoObj(&(*ite),InfoObj);
}
else if( nEventType == NX_IED_EVENT_HARDTRAP_REPORT ) // 开关量
{
    bGetInfoObj = __DoGetDiInfoObj(&(*ite),InfoObj);
}
else if( nEventType == NX_IED_EVENT_COMMU_REPORT )  // 通信状态
{
    bGetInfoObj = __DoGetCommuStatusInfoObj(Asdu1.Addr,&(*ite),InfoObj);
}
// 【新增】定值区变化事件处理
else if( nEventType == NX_SYS_EVENT_IED_SGZONE_CHG_REPORT ) // 定值区变化
{
    bGetInfoObj = __DoGetSetGroupInfoObj(&(*ite),InfoObj);
}
```

**2.2 实现新方法**

参考现有的 `__DoGetAlarmInfoObj()` 等方法的实现模式：

```cpp
/**
* @brief         获得定值区变化事件信息体标识对象信息
* @param[in]     NX_EVENT_FIELD_STRUCT * pField：事件信息子集
* @param[out]    ASDU_INFO_OBJ & InfoObj:信息对象
* @return        bool:true-成功 false-失败
*/
bool TNXEcProAsdu1GWS::__DoGetSetGroupInfoObj(IN NX_EVENT_FIELD_STRUCT * pField, OUT ASDU_INFO_OBJ & InfoObj)
{
    char cError[255] = "";
    
    if (pField == NULL)
    {
        RcdErrLogWithParentClass("__DoGetSetGroupInfoObj():传入的事件子集结构为NULL,无法获取","TNXEcProAsdu1GWS");
        return false;
    }
    
    InfoObj.nFun = 250;                    // 0xFA - 定值区功能类型
    InfoObj.nInf = 0;                      // 信息序号，在外层通过n_outaddr103设置
    InfoObj.nDpi = pField->n_value;        // 定值区号，从事件值字段获取
    
    // DPI值域限制检查（通常为1-4）
    if (InfoObj.nDpi < 1 || InfoObj.nDpi > 4)
    {
        sprintf(cError,"__DoGetSetGroupInfoObj():定值区号值%d超出有效范围[1-4]",InfoObj.nDpi);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");
        InfoObj.nDpi = 1; // 设置为默认值
    }
    
    sprintf(cError,"__DoGetSetGroupInfoObj():定值区变化事件转换成功,FUN=%d,INF=%d,DPI=%d",
        InfoObj.nFun,InfoObj.nInf,InfoObj.nDpi);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu1GWS");
    
    return true;
}
```

## 注意事项

1. **DPI值域限制**: 定值区号通常范围为1-4，需要进行边界检查
2. **事件路由**: 当前 `NX_SYS_EVENT_IED_SGZONE_CHG_REPORT` 只路由到ASDU10，可能需要修改路由配置使其同时路由到ASDU1
3. **INF字段设置**: 信息序号(INF)在外层流程中通过 `n_outaddr103` 设置，无需在InfoObj生成方法中处理
4. **错误处理**: 保持与现有代码风格一致的错误处理和日志记录
5. **事件值获取**: 直接从 `pField->n_value` 获取定值区号，这是标准的事件值传递方式

## 事件路由分析

根据代码分析，当前事件路由配置：

```cpp
// 在 NXEc60870CvtObj_GWS.cpp 中
case NX_SYS_EVENT_IED_SGZONE_CHG_REPORT:
    pAsdu = new TNXEcProAsdu10GWS(m_pModelSeek,m_pLogRecord); // 只路由到ASDU10
    break;
```

**可能需要的修改**：
- 如果需要同时支持ASDU1和ASDU10，可能需要修改事件路由逻辑
- 或者创建单独的事件类型专门用于ASDU1处理

## 风险评估

- **低风险**: 只添加新的处理分支，不修改现有功能
- **兼容性**: 保持现有接口不变，向后兼容
- **测试需要**: 需要验证定值区变化事件是否正确路由到ASDU1并正确上送

## 实现优先级

1. **高优先级**: 实现基本的定值区变化ASDU1上送功能
2. **中优先级**: 完善DPI值域检查和错误处理
3. **低优先级**: 优化事件路由配置（如果需要）

## 后续扩展

如果需要支持更多定值区相关功能：
1. 支持多CPU的定值区变化
2. 支持定值区状态查询
3. 支持定值区切换确认机制
