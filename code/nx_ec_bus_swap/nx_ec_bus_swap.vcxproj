﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5B36D257-B2EE-4107-B886-A128CBA7F15A}</ProjectGuid>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <Keyword>ManagedCProj</Keyword>
    <RootNamespace>nx_ec_bus_swap</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CLRSupport>true</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../nx_common/;../../../../platform_include/plm_common/;../../../../platform_include/plm_commun/;../../../../platform_include/plm_dbm/;../ec_common/</IncludePath>
    <OutDir>../../../../nx_bin/debug/nx_ec</OutDir>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;../../../../nx_lib/debug/;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../nx_common/;../../../../platform_include/plm_common/;../../../../platform_include/plm_commun/;../../../../platform_include/plm_dbm/;../ec_common/</IncludePath>
    <OutDir>../../../../nx_bin/release/nx_ec</OutDir>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;../../../../nx_lib/release/;</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;_USE_32BIT_TIME_T</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;_USE_32BIT_TIME_T</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\nx_common\NxmbAppMngr.h" />
    <ClInclude Include="..\ec_common\ec_bus_swap_def.h" />
    <ClInclude Include="..\ec_common\NXEcRegisterObject.h" />
    <ClInclude Include="..\ec_common\EcTemplateFunc.h" />
    <ClInclude Include="..\ec_common\ec_common_def.h" />
    <ClInclude Include="..\ec_common\NXECObject.h" />
    <ClInclude Include="..\ec_common\NXLoadEcModelLib.h" />
    <ClInclude Include="..\ec_common\NXLoadSrvMedLib.h" />
    <ClInclude Include="..\ec_common\NXObserver.h" />
    <ClInclude Include="..\ec_common\NXSubject.h" />
    <ClInclude Include="NXEcBusSwap.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\..\..\nx_common\NxmbAppMngr.cpp" />
    <ClCompile Include="..\ec_common\EcTemplateFunc.cpp" />
    <ClCompile Include="..\ec_common\NXECObject.cpp" />
    <ClCompile Include="..\ec_common\NXEcRegisterObject.cpp" />
    <ClCompile Include="..\ec_common\NXLoadEcModelLib.cpp" />
    <ClCompile Include="..\ec_common\NXLoadSrvMedLib.cpp" />
    <ClCompile Include="..\ec_common\NXObserver.cpp" />
    <ClCompile Include="..\ec_common\NXSubject.cpp" />
    <ClCompile Include="ec_bus_swap_modify_note.cpp" />
    <ClCompile Include="NXEcBusSwap.cpp" />
    <ClCompile Include="nxec_bus_swap_export.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_bus_swap.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>