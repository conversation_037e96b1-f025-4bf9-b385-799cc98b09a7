﻿/*********************************************************************
*ec_srv_sh_autostn_104_modify_note.cpp       creater:wsj     create date:08/03/2023
*-------------------------------------------------------------
*               note: 上海即插即用103服务端规约转换模块历史修改记录
*  
*********************************************************************/
/**	Ver1.0.2 2023-12-21	修改人:吴斯杰
1、修改内容:
修改测试过程中发现的问题
1）响应全站总招，生成已接入设备的信息列表，不需上送的设备过滤条件修改：改为不处理103地址小于0或者103组号=0的设备；
2）获取指定设备的状态量配置(属性结构)，不上送的设备条件修改：改为不处理103地址小于0的装置配置信息，以及设置为不上送的装置；
3）获取指定二次设备配置(描述)，不上送的设备条件修改：不处理103地址小于0的装置的配置信息以及不处理设备ID不在订单中的设备；
4）核容文件上送处理，修改获取核容文件路径方法：库中配置的录波总路径/子站名称/IED_设备编号_设备名称/；
5）召唤核容文件列表，上送文件时间为文件名中的时间，而非生成时间。
 2、影响范围：全站总召，设备信息上送；核容文件、核容文件列表上送。
 3、修改代码范围：NXEcProAsdu101_GWS.cpp、NXEcProAsdu101_GWS.h、NXEcProAsdu103_GWS.cpp、NXEcProAsdu103_GWS.h、NXEcProAsdu7_GWS.cpp、NXEcProAsdu7_GWS.h、
					NXEcProAsdu21_GWS.h、NXEcProAsdu21_Direct_GWS.cpp、NXEc60870CvtObj_GWS.h、NXEc60870CvtObj_GWS.cpp

  **/

/**	Ver1.0.1 2023-10-20	修改人:吴斯杰
1、修改内容:
（1）增加了响应主站召唤核容文件功能。
 2、影响范围：响应主站召唤核容文件功能
 3、修改代码范围：NXEcProAsdu101_GWS.cpp、NXEcProAsdu101_GWS.h、NXEcProAsdu103_GWS.cpp、NXEcProAsdu103_GWS.h
  **/

/**	Ver1.0.0 2023-08-03	修改人:吴斯杰
1、修改内容:
（1）首次创建改规约；
（2）在国网103基础上，增加响应主站召唤cid文件列表和文件功能
 2、影响范围：
 3、修改代码范围：
  **/