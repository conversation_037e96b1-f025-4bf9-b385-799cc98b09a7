﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\platform_include\FileOperate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\LogRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\MyDeque.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\ThreadMutualLock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\TimeConvert.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXECObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_fun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoadProOperationLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoadMsgOperationLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\EcProCommonFun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu1.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu4.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu6.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu7.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu10.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu12.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu13.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu15.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu16.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu17.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu18.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu21.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu42.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu101.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu102.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu103.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc60870CvtFactory_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc60870CvtObj_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcGW104SrvProtocol.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu12_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu13_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu16_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu17_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu21_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\plm_dbm\DataRow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoad60870FlowLib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu15_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\nx_errno_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu7_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu10_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu42_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu1_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu103_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProXmlHdl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\UnLibmngr_Gbk2Utf8.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcProAsdu101_GWS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ec_pro_srv_gw104_export.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc60870CvtFactory_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc60870CvtObj_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcGW104SrvProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu12_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu13_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu16_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu18.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu102.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXECObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu17_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu21_Direct_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu15_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu7_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="nx_ec_pro_srv_gw104_modify_note.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu10_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu42_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu1_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu103_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProXmlHdl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinystr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxml.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxmlerror.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxmlparser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\UnLibmngr_Gbk2Utf8.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcProAsdu101_GWS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_srv_gw104.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>Source Files</Filter>
    </None>
    <None Include="Gw103.ini">
      <Filter>Source Files</Filter>
    </None>
  </ItemGroup>
</Project>