﻿/*********************************************************************
*ec_srv_gw104_modify_note.cpp       creater:sl     create date:09/03/2015
*-------------------------------------------------------------
*               note: 国网103服务端规约转换模块历史修改记录
*  
*********************************************************************/

/**  Ver 1.0.28.1 2025-08-13 修改人：马梁
1、修改内容:
(1)修改1.0.28履历，影响范围
2. 影响范围： 
3.代码修改范围：
**/

/**  Ver 1.0.28 2025-08-13 修改人：马梁
1、修改内容:
版本号升级
新疆远方修改定值-日志中路径格式化:
(1)修改asdu101的__QueryGeneralFilesList_Settingup函数，去除未使用参数，日志使用格式化路径
(2)修改asdu103的_GeneralFileHandle_Settingup函数，去除未使用参数，日志使用格式化路径
(3)makefile
2. 影响范围： 
(1)子站本地日志文件
(2)召唤定值文件列表
3.代码修改范围：
本次共修改5个文件：NXEcProAsdu101_GWS.h、NXEcProAsdu101_GWS.cpp、NXEcProAsdu103_GWS.h、NXEcProAsdu103_GWS.cpp、makefile
**/

/**  Ver 1.0.27 2025-08-07 修改人：马梁
1、修改内容:
版本号升级
新疆远方修改定值-召唤定值文件功能需求:
(1)修改asdu101的DirectResFromlocal函数，新增__QueryGeneralFilesList_Settingup函数，实现主站召唤定值文件列表功能
(2)修改asdu103的DirectResFromLoca1函数，新增_GeneralFileHandle_Settingup函数，实现主站召唤定值文件功能
(3)新增asdu109处理,接收nx通用文件生成事件报告，并转成asdu109上送主站
(4)修改NXEc60870CvtObj_GWS.cpp，新增NX_IED_EVENT_FILE_REPORT类型事件
(5)makefile
2. 影响范围： 
(1)从子站召唤文件列表
(2)从子站召唤文件
(3)新文件通知上送
3.代码修改范围：
本次共修改9个文件：NXEc60870CvtObj_GWS.h、NXEc60870CvtObj_GWS.cpp、NXEcProAsdu101_GWS.h、NXEcProAsdu101_GWS.cpp、NXEcProAsdu103_GWS.h、NXEcProAsdu103_GWS.cpp、NXEcProAsdu109_GWS.h、NXEcProAsdu109_GWS.cpp、makefile
**/

/**  Ver 1.0.26 2025-06-23 修改人：蒋磊
1、修改内容:
(1)bug12412  
因NX总线库的限制，vector不能为空，所以查询执行时间范围内的列表为空时会主动添加一条记录，-999，从总线收到查询录波文件目录应该时需添加判断，若vector的size为1
且obj_id=-999时判断列表为空
2. 影响范围： 
(1)从装置召唤录波文件列表
3.代码修改范围：
本次共修改1个文件：NXEcProAsdu16_GWS.cpp
**/

/**  Ver 1.0.25 2025-06-12 修改人：吴斯杰
1、修改内容:
(1)甘肃新需求：bug6946
实现方式：主站召唤厂站组标题时，子站端查询对应组标题中，该主站是否有不订阅的一次设备，若全部不订阅则不再上送该组标题。
2. 影响范围： 
(1)主站初始化
3.代码修改范围：
本次共修改2个文件：NXEcProAsdu21_GWS.h、NXEcProAsdu21_Direct_GWS.cpp
**/

/**  Ver 1.0.24.4 2025-06-10 修改人：吴斯杰
1、修改内容:
(1)黑龙江机器人项目，巡视文件名中文编码问题：增加配置字段，标识文件名是gbk还是utf，在编码是utf的情况下，转换编码为gbk。
2. 影响范围： 
(1)召唤巡视文件；
(2)Gw103.ini新增robot_file_code字段：巡视文件名中文编码：0-gbk；1-utf，默认为0
3.代码修改范围：
本次共修改2个文件：NXEcProAsdu103_GWS.cpp、NXEcProAsdu103_GWS.h
**/

/**  Ver 1.0.24.3 2024-12-6 修改人：吴斯杰
1、修改内容:
	makefile版本升级
2. 影响范围： 
3.代码修改范围：
**/

/**  Ver 1.0.24.2 2024-12-5 修改人：吴斯杰
1、修改内容:
	（1）解决bug9858：召唤历史事件时，子站国网103对上上送数据异常数据，导致连接断开问题。
	原因：现场的数据库中，所有故障量信息和动作事件的故障序号都是0，子站按照规范将故障信息和动作关联，把所有故障量关联到了第一个动作事件上，导致第一个动作事件所在的报文超长，导致数据异常；
	解决方案：和杨轶森讨论后，决定不上送故障序号为0的故障量。
2. 影响范围： 
	（1）当故障序号为0时，上送的历史事件信息。
3.代码修改范围：
	（1）本次修改了NXEcProAsdu17_GWS.cpp
**/

/**  Ver 1.0.24.1 2024-01-19 修改人：吴斯杰
1、修改内容:
	（1）解决bug7420：按关键字召唤通用文件列表填空时，子站国网103对上崩溃退出问题。
	当召唤通用文件列表填空时，子站上送全部通用文件，否则获取指定文件目录上送。
2. 影响范围： 
	（1）按关键字召唤通用文件列表
3.代码修改范围：
	（1）本次修改了NXEcProAsdu101_GWS：__GetFileNameFormProFrameBody
**/

/**  Ver 1.0.24 2024-01-17 修改人：吴斯杰
1、修改内容:
	（1）实现哈尔滨新需求，从主站端召唤子站日志目录列表及日志文件（目前不支持按照时间范围召唤）。
2. 影响范围： 
	（1）主站下发召唤通用文件命令，子站匹配下发的命令中是否有nxlog，如果有，则召唤子站日志文件列表及日志文件。
3.代码修改范围：
	（1）本次修改了NXEcProAsdu101_GWS.h,NXEcProAsdu101_GWS.cpp,NXEcProAsdu103_GWS.h,NXEcProAsdu103_GWS.cpp共计4个文件
**/

/*
* 日    期: 2016-11-29
* 修 改 人: sl     
* 版    本: 1.0.1
* 修改原因: 
* 修改内容: 当收到主站从本地召唤录波文件命令后，如果本地没有，则自动改为从装置召唤.
* 修改位置: NxEc60870CvtObj_GwS ：增加三个判断是否本地存在该文件的函数。重载_GetAsdu13CvtType；
---------------------------------------------------------------------

* 日    期: 2016-12-14
* 修 改 人: sl     
* 版    本: 1.0.2
* 修改原因: 
* 修改内容: 
* 修改位置: NXEcProAsdu7_GWS.cpp:响应总招时上送运行状态是FUN号误写成240了。按文档应该是251；
---------------------------------------------------------------------

* 日    期: 2017-02-20
* 修 改 人: sl     
* 版    本: 1.0.3
* 修改原因: 上送二次设备属性结构时，没有根据命令中的组号进行过滤，导致上送了所有的组。 
* 修改内容: 增加组过滤，非本组信息，不上送。
* 修改位置: NXEcProAsdu21_Direct.cpp：TNXEcProAsdu21::_DirectReIedPropertyCfg（）
------------------------------------------------------------------------
* 日    期: 2017-03-13
* 修 改 人: sl     
* 版    本: 1.0.4 
* 修改内容: 召唤录波文件时，判断文件在本地是否存在时，文件名长度填写错误，导致不能正常识别文件名。
* 修改位置: NXEc60870CvtObj_GWS.cpp：：_GetAsdu13CvtType（）
 
/**	Ver1.0.5 2017-06-30	修改人:宋亮
修改内容:
	子站响应召唤历史动作命令是,当特征量的CPU和动作的CPU匹配不上时,规约处理有问题,导致程序异常退出.
-----
----- 发布说明:
----- 响应主站召唤历史动作时,程序可能会异常退出.
----- 
*/ 

/**	Ver1.0.6 2017-12-18	修改人:杨轶森
修改内容:
	增加对从装置召唤录波文件NX消息的响应处理，解决从召唤录波文件失败问题.涉及修改类CNXEc60870CvtObjGWS
-----
----- 发布说明:
----- 增加对从装置召唤录波文件NX消息的响应处理，解决从召唤录波文件失败问题..
----- 
*/ 

/**	Ver1.0.7 2017-12-18	修改人:杨轶森
修改内容:
	根据新疆需求.支持软压板变位主动上送和定值区号变化主动上送.涉及修改类TNXEcProAsdu10GWS
-----
----- 发布说明:
----- 根据新疆需求.支持软压板变位主动上送和定值区号变化主动上送.
----- 
*/ 
/**	Ver1.0.8 2017-12-20	修改人:杨轶森
修改内容:
	软压板变位及定值区号变化主动上送报文中，填写真实的103地址与CPU.涉及修改类TNXEcProAsdu10GWS
-----
----- 发布说明:
----- 软压板变位及定值区号变化主动上送报文中，填写真实的103地址与CPU.
----- 
*/ 

/**	Ver1.0.9 2017-12-21	修改人:杨轶森
修改内容:
	解决下发从装置召唤录波命令,核对文件名时,由于文件名带有路径，导致核对文件名失败，从而召唤录波失败情况.涉及修改类TNXEcProAsdu13GWS
-----
----- 发布说明:
----- 解决下发从装置召唤录波命令,核对文件名时,由于文件名带有路径，导致核对文件名失败，从而召唤录波失败情况.
----- 
*/ 

/**	Ver1.0.10 2018-06-15	修改人:杨轶森
修改内容:
	修改录波文件不存在时没有回应失败报文给主站的BUG
-----
----- 发布说明:
----- 修改录波文件不存在时没有回应失败报文给主站的BUG.
----- 
*/ 
/**	Ver1.0.11 2018-06-23	修改人:杨轶森
修改内容:
	兼容硬压板管控系统数据.
-----
----- 发布说明:
----- 兼容硬压板管控系统数据.
----- 
*/ 
/**	Ver1.0.12 2018-11-13	修改人:杨轶森
修改内容:
	解决挂载在虚设备上的硬压板召唤不上来的BUG.
-----
----- 发布说明:
----- 解决挂载在虚设备上的硬压板召唤不上来的BUG.
----- 
*/ 
/**	Ver1.0.13 2019-07-15	修改人:杨轶森
修改内容:
	增加配置文档读取，增加召唤信息记录到审计日志中.
-----
----- 发布说明:
----- 增加配置文档读取，增加召唤信息记录到审计日志中.记录到数据库nx_t_usr_oprlog表中
----- 
*/ 
/**	Ver1.0.14 2020-02-12	修改人:杨轶森
修改内容:
	增加对机器人巡视功能的处理.涉及修改类TNXEcProAsdu10GWS,增加类TNXEcProAsdu103GWS
-----
----- 发布说明:
----- 增加对机器人巡视功能的处理.
----- 
*/ 
/**	Ver1.0.15 2020-12-08	修改人:杨轶森
修改内容:
	增加类TNXEcProAsdu103GWS,以处理对于机器人巡视文件召唤
-----
----- 发布说明:
----- 增加类TNXEcProAsdu103GWS,以处理对于机器人巡视文件召唤
----- 
*/ 
/**	Ver1.0.16 2021-03-30	修改人:杨轶森
修改内容：
1、满足新疆对点项目功能，支持对点命令下发、XML文件生成。
2、影响范围：增加GW103配置文档，老的现场如果没有配置文档默认不使用新疆对点功能，故无影响。
3、涉及修改NXEcProAsdu10_GWS.cpp、NXEcProAsdu10_GWS.h、NXEcProXmlHdl.cpp、NXEcProXmlHdl.h
*/ 

/**	Ver1.0.17 2021-09-06	修改人:杨轶森
1、修改内容:
（1）针对1.0.11版本压板管控功能已在主站端特殊处理，子站无需处理，去除原来处理机制，按正常压板召唤流程。
	同时解决，因对压板管控的特殊处理导致定值修改时，也会按压板处理流程走，导致查不到配置，无法下发修改定值的BUG。
 2、影响范围：
	原BUG导致无法下发定值修改命令。
  3、修改代码范围：
  （1）本次修改了NXEcProAsdu10_GWS.cpp共计1个文件；
  **/ 

/**	Ver1.0.18 2021-09-07	修改人:孟俊如
1、修改内容:
	针对新疆地区进行功能扩展
2、影响范围：
	新增了专门的通用文件召唤功能和召唤定值区号上送报文条目号固定为1，
	以及为了判断是否要启用新增的扩展功能而在asdu10、asdu101中增加了读取Gw103.ini的功能
3、修改代码范围：
  （1）本次修改了：	NXEc60870CvtObj_GWS.h;NXEc60870CvtObj_GWS.cpp;NXEcProAsdu10_GWS.h;NXEcProAsdu10_GWS.cpp;Gw103.ini共计5个文件；
	   本次新增了：	NXEcProAsdu101_GWS.h;NXEcProAsdu101_GWS.cpp共计2个文件。
  **/ 

/**	Ver1.0.19 2021-10-09	修改人:孟俊如
1、修改内容:
	针对录波文件召唤功能扩展
2、影响范围：
	支持了录波文件在本地已存放，而尚未入库的情况下，也能够让主站成功召唤录波文件。
	这种特殊情况下，回复的录波文件报文中：
	“故障时间”使用的是本地录波文件的“最后修改时间”，
	“录波文件名”使用的是主站下发的文件名。
	（而如果录波记录已经入库，则是去读取数据库中的故障时间，去数据库中模糊匹配到文件名）	
3、修改代码范围：
  （1）本次修改了：NXEcProAsdu13_GWS.h;NXEcProAsdu13_GWS.cpp;共计2个文件；
  **/ 


/**	Ver1.0.20 2021-11-04	修改人:孟俊如
1、修改内容:
	去除了重载的__CvtAsdu10InfoStructToCommonMsg，沿用基类中的
2、影响范围：
	之前版本ver1.0.18中误操作，未使用基类中的__CvtAsdu10InfoStructToCommonMsg造成现场使用产生了问题
3、修改代码范围：
  （1）本次修改了：NXEcProAsdu10_GWS.h;NXEcProAsdu10_GWS.cpp;共计2个文件；
  **/ 


/**  Ver 1.0.20 2021-11-05 修改人：孟俊如
1、修改内容:
    （1）重新解决BUG1883，恢复1.0.17版本中变更：取消压板管控功能（该功能影响响应主站控制命令）
2. 影响范围： 
      控制功能－响应主站控制命令
**/

//详细内容如下所述
/**
1、修改内容:
（1）由于1.0.18版本初次提交时，从本地的代码中上传了SVN而未Update，导致1.0.17版本中的问题又被包含进去了.
（2）原因描述引用自1.0.17版本中：“针对1.0.11版本压板管控功能已在主站端特殊处理，子站无需处理，去除原来处理机制，按正常压板召唤流程。
同时解决，因对压板管控的特殊处理导致定值修改时，也会按压板处理流程走，导致查不到配置，无法下发修改定值的BUG。”
（3）解决BUG：尚无BUG提出。
2、影响范围：
（1）控制功能－响应主站控制命令:原BUG导致无法下发定值修改命令。
3、修改代码范围：
（1）本次修改了NXEcProAsdu10_GWS.cpp，NXEcProAsdu10_GWS.h共计2个文件。
（2）在TNXEcProAsdu10GWS类中去除了__CvtAsdu10InfoStructToCommonMsg方法。
**/


/**  Ver 1.0.20.1 2021-11-29 修改人：孟俊如
1、修改内容:
	（1）更正makefile的版本为1.0.20.1
2. 影响范围： 
    （1）更正makefile的版本为1.0.20.1
	（2）配置文件变更：
	 Ver 1.0.20 无变化
3.代码修改范围：
	（1）本次修改了makefile共计1个文件
**/

/**  Ver 1.0.21 2022-04-11 修改人：杨轶森
1、修改内容:
	（1）解决召唤机器人巡视报告时，拼凑的路径缺少“/”导致召唤路径判断错误的BUG
	（2）增加下发机器人巡视命令时的关键日志输出
2. 影响范围： 
    （1）影响对于巡视报告的召唤功能失败
	（2）配置文件变更：
	 Ver 1.0.20 无变化
3.代码修改范围：
	（1）本次修改了NXEcProAsdu10_GWS.cpp NXEcProAsdu103_GWS.cpp共计2个文件
**/

/**  Ver 1.0.22 2023-09-16 修改人：杨轶森
1、修改内容:
	（1）满足新疆对点项目新需求《02-新疆电科院-保信+自动测试-技术方案-20220708》
	（2）满足对SafetyProcess类型读值处理，生成ATS_TEST_CMD.xml中，新增SafetyProcess指令的参数
2. 影响范围： 
    （1）影响对点文件内容格式
3.代码修改范围：
	（1）本次修改了NXEcProXmlHdl.cpp NXEcProXmlHdl.h共计2个文件
**/

/**  Ver 1.0.23 2023-09-16 修改人：杨轶森
1、修改内容:
	（1）重载ASDU17，解决召唤历史告警时，子站接收数据未填写，导致主站收到告警接收时间为2070年
2. 影响范围： 
	（1）历史告警接收时间
3.代码修改范围：
	（1）本次修改了NXEcProAsdu17_GWS.h共计2个文件
**/