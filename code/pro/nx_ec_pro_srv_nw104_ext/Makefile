# 对外通信-南网104扩展规约库  sl

# 头文件包含路径
INCLUDE_PATH = -I../../../../../nx_common/  \
               -I../../../../../platform_include/plm_common/  \
               -I../../../../../platform_include/plm_commun/  \
               -I../../../../../platform_include/plm_dbm/  \
               -I../../ec_common/  \
               -I../ec_pro_common/ \
               -I../ec_pro_common_iec60870/ \
               -I./  
               
# 平台名称(默认LINUX)
PLATNAME =__PLATFORM_OPEN_LINUX__

# linux平台下相关标识定义
ifeq ($(PLATNAME),__PLATFORM_OPEN_LINUX__)
    PLAT_LIBS = -rdynamic
    PLAT_FLAGS = 
endif 
         
# AIX平台定义
ifeq ($(PLATNAME),__PLATFORM_AIX_UNIX__)
    PLAT_LIBS =
    PLAT_FLAGS = -Wl,-G -fno-strict-aliasing 
endif 

# SUN平台定义
ifeq ($(PLATNAME),__PLATFORM_SUN_UNIX__)
    PLAT_LIBS = -lsocket
    PLAT_FLAGS = -B direct -z lazyload -z ignore
endif 

# HP平台定义
ifeq ($(PLATNAME),__PLATFORM_HP_UNIX__)
    PLAT_LIBS = 
    PLAT_FLAGS = -Wl,-G -fno-strict-aliasing -mlp64
endif 

# 发行版或调试版定义
_D =Y
ifeq ($(_D),Y)
     RUN_FLAGS = -g -D_DEBUG
     SYLIB_PATH  = -L../../../../../nx_lib/debug/
     ECLIB_PATH  = -L../../../../../nx_lib/debug/nx_ec/
     OUT_PATH    =../../../../../nx_bin/debug/nx_ec/
     OBJ_PATH    =./debug/
else
     RUN_FLAGS = -O2
     SYLIB_PATH  = -L../../../../../nx_lib/release/
     ECLIB_PATH  = -L../../../../../nx_lib/release/nx_ec/
     OUT_PATH    =../../../../../nx_bin/release/nx_ec/
     OBJ_PATH    =./release/
endif


# 依赖的运行库(sylib-平台公用文件 plmdb-访问数据库公用文件 )          
SY_LIBS = $(SYLIB_PATH) -lplm_db -lsylib 
EC_LIBS = $(ECLIB_PATH) -lec_pro_common_iec60870 -lec_pro_common -lec_common 

LIBS = -lpthread -ldl $(PLAT_LIBS) $(EC_LIBS) $(SY_LIBS)

# 编译标识
CFLAGS =$(RUN_FLAGS) -w -fpic -shared $(PLAT_FLAGS) -D$(PLATNAME) 

# 目标文件
OBJS   = $(OBJ_PATH)ec_pro_srv_nw104_ext_export.o $(OBJ_PATH)NXEcNW104EXT60870CvtFactory.o $(OBJ_PATH)NXEcNW104EXT60870CvtObj.o\
	       $(OBJ_PATH)NXEcNW104EXTProAsdu101.o $(OBJ_PATH)NXEcNW104EXTProAsdu103.o $(OBJ_PATH)NXEcNW104EXTProAsdu12.o $(OBJ_PATH)NXEcNW104EXTProAsdu1.o\
	       $(OBJ_PATH)NXEcNW104EXTProAsdu13.o $(OBJ_PATH)NXEcNW104EXTProAsdu15.o $(OBJ_PATH)NXEcNW104EXTProAsdu16.o $(OBJ_PATH)NXEcNW104EXTProAsdu17.o \
	       $(OBJ_PATH)NXEcNW104EXTProAsdu2.o $(OBJ_PATH)NXEcNW104EXTProAsdu20.o $(OBJ_PATH)NXEcNW104EXTProAsdu21_Direct.o \
	       $(OBJ_PATH)NXEcNW104EXTProAsdu7.o $(OBJ_PATH)NXEcNW104EXTSrvProtocol.o  $(OBJ_PATH)CsgLogRecord.o $(OBJ_PATH)CsgLogRecordMngr.o \
		   $(OBJ_PATH)NXEcNW104EXTProAsdu105.o $(OBJ_PATH)NXEcNW104EXTProAsdu107.o  $(OBJ_PATH)NXEcNW104EXTProAsdu10.o  $(OBJ_PATH)NXEcFileCommon.o 

# 编译器
CC = g++ 

VER=1.0.9

libnx_ec_pro_srv_nw104_ext.so-$(VER) : mkobjdir $(OBJS) mklibdir 
	$(CC) -o $(OUT_PATH)libnx_ec_pro_srv_nw104_ext.so-$(VER) $(OBJS) $(CFLAGS) $(LIBS) $(INCLUDE_PATH)

$(OBJ_PATH)NXEcNW104EXTSrvProtocol.o : NXEcNW104EXTSrvProtocol.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTSrvProtocol.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTSrvProtocol.cpp

$(OBJ_PATH)ec_pro_srv_nw104_ext_export.o : ec_pro_srv_nw104_ext_export.cpp
	$(CC) -o $(OBJ_PATH)ec_pro_srv_nw104_ext_export.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c ec_pro_srv_nw104_ext_export.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu10.o : NXEcNW104EXTProAsdu10.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu10.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu10.cpp
	      
$(OBJ_PATH)NXEcNW104EXT60870CvtFactory.o : NXEcNW104EXT60870CvtFactory.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXT60870CvtFactory.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXT60870CvtFactory.cpp


$(OBJ_PATH)NXEcNW104EXT60870CvtObj.o : NXEcNW104EXT60870CvtObj.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXT60870CvtObj.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXT60870CvtObj.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu101.o : NXEcNW104EXTProAsdu101.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu101.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu101.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu1.o : NXEcNW104EXTProAsdu1.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu1.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu1.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu103.o : NXEcNW104EXTProAsdu103.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu103.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu103.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu12.o : NXEcNW104EXTProAsdu12.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu12.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu12.cpp
	      	      
$(OBJ_PATH)NXEcNW104EXTProAsdu13.o : NXEcNW104EXTProAsdu13.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu13.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu13.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu16.o : NXEcNW104EXTProAsdu16.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu16.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu16.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu15.o : NXEcNW104EXTProAsdu15.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu15.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu15.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu17.o : NXEcNW104EXTProAsdu17.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu17.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu17.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu2.o : NXEcNW104EXTProAsdu2.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu2.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu2.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu20.o : NXEcNW104EXTProAsdu20.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu20.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu20.cpp
	      
$(OBJ_PATH)NXEcNW104EXTProAsdu21_Direct.o : NXEcNW104EXTProAsdu21_Direct.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu21_Direct.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu21_Direct.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu7.o : NXEcNW104EXTProAsdu7.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu7.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu7.cpp

 $(OBJ_PATH)NXEcNW104EXTProAsdu105.o : NXEcNW104EXTProAsdu105.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu105.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu105.cpp

$(OBJ_PATH)NXEcNW104EXTProAsdu107.o : NXEcNW104EXTProAsdu107.cpp
	$(CC) -o $(OBJ_PATH)NXEcNW104EXTProAsdu107.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcNW104EXTProAsdu107.cpp

$(OBJ_PATH)NXEcFileCommon.o : NXEcFileCommon.cpp
	$(CC) -o $(OBJ_PATH)NXEcFileCommon.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c NXEcFileCommon.cpp	 

$(OBJ_PATH)CsgLogRecord.o : ../../../../../nx_common/CsgLogRecord.cpp 
	$(CC) -o $(OBJ_PATH)CsgLogRecord.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c ../../../../../nx_common/CsgLogRecord.cpp

$(OBJ_PATH)CsgLogRecordMngr.o : ../../../../../nx_common/CsgLogRecordMngr.cpp 
	$(CC) -o $(OBJ_PATH)CsgLogRecordMngr.o $(CFLAGS) $(LIBS) $(INCLUDE_PATH)\
	      -c ../../../../../nx_common/CsgLogRecordMngr.cpp  

mkobjdir:
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists;";   else mkdir -p $(OBJ_PATH); fi

mklibdir:
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists;";   else mkdir -p $(OUT_PATH); fi

.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean

clean :
		-rm $(OUT_PATH)libnx_ec_pro_srv_nw104_ext.so-$(VER) $(OBJS)
