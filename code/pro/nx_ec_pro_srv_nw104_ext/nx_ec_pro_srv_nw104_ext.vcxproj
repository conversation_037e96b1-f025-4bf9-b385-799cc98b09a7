﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{05E817DE-272A-4386-91C8-295695CFEA58}</ProjectGuid>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <Keyword>ManagedCProj</Keyword>
    <RootNamespace>nx_ec_pro_srv_nw104_ext</RootNamespace>
    <ProjectName>nx_ec_pro_srv_nw104_ext</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CLRSupport>true</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/;$(IncludePath)</IncludePath>
    <OutDir>../../../../../nx_bin/debug/nx_ec</OutDir>
    <LibraryPath>E:\tsinsource\phoenix\trunk\nx_lib\debug;../../../../../nx_lib/debug/;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/</IncludePath>
    <LibraryPath>..\..\..\..\..\nx_lib\release;$(LibraryPath)</LibraryPath>
    <OutDir>../../../../../nx_bin/release/nx_ec</OutDir>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;../../../../../nx_lib/release/</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_PROTOCOL_EXPORT;_USE_32BIT_TIME_T;_CONSOLE</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_PROTOCOL_EXPORT;_USE_32BIT_TIME_T;_CONSOLE</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_db.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SubSystem>
      </SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecord.h" />
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_fun.h" />
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h" />
    <ClInclude Include="..\..\ec_common\NXECObject.h" />
    <ClInclude Include="..\..\ec_common\NXLoadEcModelLib.h" />
    <ClInclude Include="..\ec_pro_common\EcProCommonFun.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoad60870FlowLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoadMsgOperationLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoadProOperationLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcProtocol.h" />
    <ClInclude Include="..\ec_pro_common\NXEcSrvProtocol.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu1.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu10.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu101.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu102.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu103.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu12.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu13.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu15.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu16.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu17.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu18.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu2.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu21.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu4.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu42.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu6.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu7.h" />
    <ClInclude Include="NXEcFileCommon.h" />
    <ClInclude Include="NXEcNW104EXT60870CvtFactory.h" />
    <ClInclude Include="NXEcNW104EXT60870CvtObj.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu1.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu10.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu101.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu103.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu105.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu107.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu12.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu13.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu15.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu16.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu17.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu2.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu20.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu21.h" />
    <ClInclude Include="NXEcNW104EXTProAsdu7.h" />
    <ClInclude Include="NXEcNW104EXTSrvProtocol.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp" />
    <ClCompile Include="..\..\ec_common\NXECObject.cpp" />
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp" />
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp" />
    <ClCompile Include="..\ec_pro_common\ec_pro_common_modify_note.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\ec_pro_60870_modify_note.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu102.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu18.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp" />
    <ClCompile Include="NXEcFileCommon.cpp" />
    <ClCompile Include="NXEcNW104EXT60870CvtFactory.cpp" />
    <ClCompile Include="NXEcNW104EXT60870CvtObj.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu1.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu10.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu101.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu103.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu105.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu107.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu12.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu13.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu15.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu16.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu17.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu2.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu20.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu21_Direct.cpp" />
    <ClCompile Include="NXEcNW104EXTProAsdu7.cpp" />
    <ClCompile Include="NXEcNW104EXTSrvProtocol.cpp" />
    <ClCompile Include="ec_pro_srv_nw104_ext_export.cpp" />
    <ClCompile Include="nx_ec_pro_srv_nw104_ext_modify_note.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_srv_nw104_ext.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>