/**********************************************************************
* NXEcSrvMsgOperaObj.h         author:jjl      date:15/11/2013            
*---------------------------------------------------------------------
*  note: 服务端NX消息业务处理类头文件定义                                                                
*  
**********************************************************************/

#ifndef _H_NXECSRVMSGOPERAOBJ_H_ 
#define _H_NXECSRVMSGOPERAOBJ_H_

#include "NXEcMsgOperationObj.h"
#include "NXObserver.h"
#include "TimeConvert.h"
#include "NxYKStrapRead.h"


/////////////////////////////////////////////////////////////////结构定义

/** @brief         等待关联事件结构*/
typedef struct _WAIT_EVENT_INFO
{
	/** @brief     接收时间*/
	time_t         nRcvTime;

	/** @brief     事件内容的秒时间*/
	time_t         nEventTime;

	/** @brief     事件内容的毫秒时间*/
	int            nMilSecond;

	/** @brief     事件内容*/
	NX_EVENT_MESSAGE EventMsg;

	_WAIT_EVENT_INFO()
	{
		nRcvTime =0;
		nEventTime=0;
		nMilSecond=0;
	}
}WAIT_EVENT_INFO;

/** @brief         等待关联事件信息列表*/
typedef list<WAIT_EVENT_INFO> WAIT_EVENT_INFO_LIST;

/** @brief         设备ID与等待事件信息映射表*/
typedef map<int,WAIT_EVENT_INFO_LIST*> EC_IED2WAITEVENTMAP;

/**
* @defgroup  CNXEcSrvMsgOperaObj:服务端消息业务处理类  
* @{
*/
 
/**
 * @brief      服务端NX消息业务相关处理，如信息过滤、设备定制等
 * <AUTHOR>
 * @date       15/11/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class CNXEcSrvMsgOperaObj:public TNXEcMsgOperationObj
{

	//////////////////////////////////////////////////////////////////////构造析构
public:
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~CNXEcSrvMsgOperaObj();

    /**
	* @brief         构造函数 
	* @param[in]     const SRV_PRO_NXMSG_OPERA_PARAM * pParam:服务端启动参数指针
	* @param[out]    无
	* @return        无
	*/
	CNXEcSrvMsgOperaObj(IN const SRV_PRO_NXMSG_OPERA_PARAM * pParam);

	///////////////////////////////////////////////////////////////公用方法
public:

	/**
	* @brief         发送事件消息到系统内部
	* @param[in]     NX_EVENT_MESSAGE & Msg :事件消息结构
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int SendEventMsg( IN NX_EVENT_MESSAGE & Msg) ;

	/**
	* @brief         发送通用消息到系统内部
	* @param[in]     NX_COMMON_MESSAGE & Msg :通用消息结构
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int SendCommonMsg( IN NX_COMMON_MESSAGE & Msg) ;

	///////////////////////////////////////////////////////////////////////重载父类保护方法
protected:

	/**
	* @brief         将要开辟的各线程信息初始化后加入线程队列
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int      __InitThreadInfo();

	/**
	* @brief         初始化注册对象(观察者或目标者)
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int      __InitRegisterObj();

	/**
	* @brief         释放注册对象(观察者或目标者)
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int      __FreeRegisterObj() ;

	/** 
	* @brief         恢复与规约库的正常数据交换后的后续处理
	* @param[in]     无
	* @param[out]    无
	* @return        bool true-成功 false-失败
	*/
	virtual bool     __RestoreDataExchangeHandle();

	///////////////////////////////////////////////////////////////////////自身保护方法
protected:
	/**
	* @brief         观察者对象资源分派，设置注册信息等
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int      __InitObserver();

	/**
	* @brief         召唤结果信息接收处理(由观察者回调)
	* @param[in]     LPVOID pRegObj：注册对象
	* @param[in]     NX_COMMON_MESSAGE & ResultMsg:收到的结果信息
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	static int      __OnCallResultRecv(LPVOID pRegObj,NX_COMMON_MESSAGE & ResultMsg);

	/**
	* @brief         自动上送事件信息接收处理(由观察者回调)
	* @param[in]     LPVOID pRegObj：注册对象
	* @param[in]     NX_EVENT_MESSAGE & EventMsg:收到的事件信息
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	static int      __OnEventRecv(LPVOID pRegObj,NX_EVENT_MESSAGE & EventMsg);

	/**
	* @brief         释放观察者信息资源，从服务中介注销
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int     __FreeObserver();

	/**
	* @brief         事件过滤处理线程回调执行函数
	* @param[in]     LPVOID pObj: 回调对象
	* @param[in]     LPVOID pParam: 参数
	* @param[in]     无
	* @return        int: 0-成功 其它：错误码
	*/
	static int     __OnEventFilterThreadExec(LPVOID pObj,LPVOID  pParam = NULL);

	/**
	* @brief         事件过滤处理循环
	* @param[in]     无
	* @param[in]     无
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int    __EventFilterLoop();

	/**
	* @brief         指定事件的过滤处理
	* @param[in]     NX_EVENT_MESSAGE &EventMsg:指定的事件信息
	* @param[in]     无
	* @return        int: 0-需要发送 其它：无需发送
	*/
	virtual int    __EventFilterHandle(IN NX_EVENT_MESSAGE &EventMsg);

	/**
	* @brief         指定事件的检修过滤处理
	* @param[in]     NX_EVENT_MESSAGE &EventMsg:指定的事件信息
	* @return        bool: true-需要发送 false：无需发送
	*/
	virtual bool    __DebugInfoFilter(IN NX_EVENT_MESSAGE &EventMsg);

	/**
	* @brief         指定事件的信息级别过滤处理
	* @param[in]     NX_EVENT_MESSAGE &EventMsg:指定的事件信息
	* @return         bool: true-需要发送 false：无需发送
	*/
	virtual bool    __InfoLevelFilter(IN NX_EVENT_MESSAGE &EventMsg);

	/**
	* @brief         根据信息级别判断指定告警事件信息点是否上送
	* @param[in]     NX_EVENT_FIELD_STRUCT & EventField:指定的告警点信息
	* @return         bool: true-需要发送 false：无需发送
	*/
	virtual bool    __IsIedAlarmPointSend(IN NX_EVENT_FIELD_STRUCT & EventField);

	/**
	* @brief         根据信息级别判断指定动作事件信息点是否上送
	* @param[in]     NX_EVENT_FIELD_STRUCT & EventField:指定的动作点信息
	* @return         bool: true-需要发送 false：无需发送
	*/
	virtual bool   __IsIedEventPointSend(IN NX_EVENT_FIELD_STRUCT & EventField);

	/**
	* @brief         命令处理线程回调执行函数
	* @param[in]     LPVOID pObj: 回调对象
	* @param[in]     LPVOID pParam: 参数
	* @param[in]     无
	* @return        int: 0-成功 其它：错误码
	*/
	static int     __OnCmdThreadExec(LPVOID pObj,LPVOID  pParam = NULL);

	/**
	* @brief         命令处理循环
	* @param[in]     无
	* @param[in]     无
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int    __CmdLoop();

	/**
	* @brief         召唤命令处理
	* @param[in]     NX_COMMON_MESSAGE & Msg:召唤命令
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int    __CallCmdHandle(IN NX_COMMON_MESSAGE & CmdMsg);

	/**
	* @brief         控制命令处理
	* @param[in]     NX_COMMON_MESSAGE & Msg:控制命令
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __CtrlCmdHandle(IN NX_COMMON_MESSAGE & Msg);

	/**
	* @brief         对时命令处理
	* @param[in]     NX_COMMON_MESSAGE & Msg:对时命令
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __SetTimeCmdHandle(IN NX_COMMON_MESSAGE & Msg);

	/**
	* @brief         生成各装置的对时命令
	* @param[in]     int nUtcSecond:秒
	* @param[in]     int nMilSec:毫秒
	* @param[out]     NX_COMMON_MSG_LIST& CmdList:保存对时命令列表
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __MakeSetDevTimeCmd(IN int nUtcSecond,IN int nMilSec,OUT NX_COMMON_MSG_LIST& CmdList);

	/**
	* @brief         发送召唤结果给规约对象
	* @param[in]     NX_COMMON_MESSAGE & Msg:结果
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __SendCallResultToProObj(IN NX_COMMON_MESSAGE & Msg);

	/**
	* @brief         发送事件信息给规约对象
	* @param[in]     NX_EVENT_MESSAGE & Msg:事件信息
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __SendEventToProObj(IN NX_EVENT_MESSAGE & Msg);

	/**
	* @brief         初始化客户端不订阅的事件列表及动作、告警的过滤配置信息
	* @param[out]    EC_INFO_ORDER_LIST & NotOrderEventList:不订阅的事件列表
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __InitNotOrderEventList(OUT EC_INFO_ORDER_LIST & NotOrderEventList);

	/**
	* @brief         初始化客户端不订阅的设备列表
	* @param[out]    EC_DEV_ORDER_LIST & NotOrderDevList:不订阅的设备列表
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __InitNotOrderDevList(OUT EC_DEV_ORDER_LIST & NotOrderDevList);

	/**
	* @brief         网络中断期间，保存事件信息到磁盘
	* @param[in]     NX_EVENT_MESSAGE & Msg:事件信息
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __SaveEventToDisk(IN NX_EVENT_MESSAGE & Msg);

	/**
	* @brief         网络恢复后，从磁盘读取保存的事件信息到事件队列
	* @param[in]     NX_EVENT_MESSAGE & Msg:事件信息
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __ReadEventFromDisk();

	/**
	* @brief         从数据库获取设备的最新一次开关量值
	* @param[in]     NX_COMMON_MESSAGE& CmdMsg:命令
	* @param[out]    NX_COMMON_MESSAGE& ResultMsg:结果
	* @return        int: 0-成功 其它：错误码
	*/
	virtual int __GetIedHardStrapDataFromDb(IN NX_COMMON_MESSAGE& CmdMsg,OUT NX_COMMON_MESSAGE& ResultMsg);

	/**
	* @brief         是否需要关联合并处理,需要则合并
	* @param[in]     NX_EVENT_MESSAGE& EventMsg:事件信息
	* @return        bool: true-需要合并 false：不需要合并
	*/
	virtual bool __IsNeedMerge(IN NX_EVENT_MESSAGE & EventMsg);

	/**
	* @brief         发送关联合并事件相关处理
	* @param[in]     无
	* @return        int 0-成功 其它-失败
	*/
	virtual int  __SendMergedEventHandle();


	/**
	* @brief	    根据当前通道查询所属客户端下所有通道的通信状态。如果有一个通道为为通，则返回客户端的通信状态为通。			
	* @param[out]    bMyChlIdIsBiggest:本通道编号是否为最大的一个。只有编号最大的通道才去再客户端断开的情况下写缓存文件
	* @return		0-客户端为断开；-1-查询失败；1-客户端为通。
	* @note 
	**/
	int __GetClientStatus(bool & bMyChlIdIsBiggest);
	///////////////////////////////////////////////////////////////////////私有方法
private:
	/**
	* @brief		将事件消息结构体格式化成字符流以便进行保存。			
	* @param[in]	NX_EVENT_MESSAGE & Msg：待格式化的消息结构
	* @param[in]	cvector<u_int8> & vStream：数据流。
	**/
	void __CvtEventMsgToStream(NX_EVENT_MESSAGE & Msg,vector<u_int8> & vStream);

	/**
	* @brief		将数据流写入指定的文件，写入前判断文件创建日期是否为当前日期：是-追加；否-重写。			
	* @param[in]    vector<u_int8> & vStream：数据流
	* @param[in]    const char * cFileName：文件名
	**/
	void __WriteStream(vector<u_int8> & vStream,const char * cFileName);

	/**
	* @brief		将字符流转成事件消息结构体。			
	* @param[in]	NX_EVENT_MESSAGE & Msg：消息结构
	* @param[in]	const char * pStream：数据流内存指针。
	* @param[in]	int nLen:数据流长度
	**/
	bool __CvtStreamToEventMsg(IN vector<u_int8> & vStream, OUT NX_EVENT_MESSAGE & Msg);

	/**
	* @brief		将文件内容转成事件结构体。			
	* @param[in]    strFileName：文件
	**/
	void __CvtFiletoEventMsg(string & strFileName);

	/**
	* @brief		获取主站离线时缓存事件的第几天。用来做成文件名后缀。			
	* @return		第几天
	* @note 
	**/
	int __GetFileSerialNo();
	//////////////////////////////////////////////////////////////////////保护成员

	
protected:

	/** @brief         服务端消息业务处理参数*/
	const SRV_PRO_NXMSG_OPERA_PARAM * m_pSrvParam;

	/** @brief         观察者对象指针*/
	CNXObserver *      m_pObserver;

	/** @brief         事件队列*/
	CMyDeque<NX_EVENT_MESSAGE> m_EventDeque;

	/** @brief         命令队列*/
	CMyDeque<NX_COMMON_MESSAGE> m_CmdDeque;

	/** @brief         客户端发送告警类信息的配置*/
	const ECU_ORDER_TB  *    m_pClientSendAlarmCfg;

	/** @brief         客户端发送动作类信息的配置*/
	const ECU_ORDER_TB  *    m_pClientSendEventCfg;

	/** @brief         等待关联事件信息队列*/
	EC_IED2WAITEVENTMAP m_IedToWaitEventMap;

	/** @brief         等待关联事件锁*/
	CThreadMutualLock   m_LockForWaitEventMap;


	//////////////////////////////////////////////////////////////////////私有成员
private:
	//本客户端缓存离线数据的的路径。
	string				m_strOffLineDataPath;

	//主站离线后第一次开始记录事件的时间。
	time_t				m_tFirstOffLineEvent;
};

/** @} */ //CNXEcSrvMsgOperaObj OVER

#endif  // _H_NXECSRVMSGOPERAOBJ_H_