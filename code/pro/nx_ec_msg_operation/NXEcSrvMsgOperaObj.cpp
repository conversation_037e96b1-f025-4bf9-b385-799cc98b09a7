/**********************************************************************
* NXEcSrvMsgOperaObj.cpp         author:jjl      date:15/11/2013            
*---------------------------------------------------------------------
*  note:服务端NX消息业务处理类实现文件定义                                                              
*  
**********************************************************************/

#include "NXEcSrvMsgOperaObj.h"

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
CNXEcSrvMsgOperaObj::~CNXEcSrvMsgOperaObj()
{
	// 清空命令队列
	NX_COMMON_MESSAGE CmdMsg;
	while( m_CmdDeque.size() > 0 )
	{
		CmdMsg = m_CmdDeque.front();
		CmdMsg.list_subfields.clear();
		m_CmdDeque.pop_front();
	}

	// 清空事件队列
	NX_EVENT_MESSAGE EventMsg;
	while( m_EventDeque.size() > 0 )
	{
		EventMsg = m_EventDeque.front();

		if( m_pSrvParam->pClientCfg->bSaveIndisconn )
		{
			__SaveEventToDisk(EventMsg);
		}

		EventMsg.list_subfields.clear();
		m_EventDeque.pop_front();
	}

	// 清空合并队列
	CAutoLockOnStack tmpLock(&m_LockForWaitEventMap);
	WAIT_EVENT_INFO_LIST * pList = NULL;
	EC_IED2WAITEVENTMAP::iterator ite = m_IedToWaitEventMap.begin();
	while(ite != m_IedToWaitEventMap.end() )
	{
		pList = ite->second;
		if( pList != NULL )
		{
			pList->clear();
			delete pList;
			pList = NULL;
		}
		++ite;
	}
	m_IedToWaitEventMap.clear();
}

/**
* @brief         构造函数 
* @param[in]     const SRV_PRO_NXMSG_OPERA_PARAM * pParam:服务端启动参数指针
* @param[out]    无
* @return        无
*/
CNXEcSrvMsgOperaObj::CNXEcSrvMsgOperaObj(IN const SRV_PRO_NXMSG_OPERA_PARAM * pParam)
	:TNXEcMsgOperationObj(pParam->pLogRecord)
{
	m_pSrvParam = pParam;

	// 设置日志记录中实例化类名
	_SetLogClassName("CNXEcSrvMsgOperaObj");

	m_pObserver = NULL;
	m_pClientSendAlarmCfg = NULL;
	m_pClientSendEventCfg = NULL;

	m_EventDeque.clear();
	m_CmdDeque.clear();

	//生成离线文件夹字符串。考虑到一个主站存在多个通道的可能，离线文件以主站为单位生成，而不是以通道为单位生成。
	//离线文件存放路径ec/RemoteClient/OfflineData/客户端编号_客户端名称
	string strMoudleName = "nx_ec/RemoteClient";
	m_pSrvParam->pLogRecord->GetLogRecordFileRootPath(m_strOffLineDataPath);
	
// 	int nPos = m_strOffLineDataPath.find("RemoteClient") + strlen("RemoteClient");
// 	strMoudleName = m_strOffLineDataPath.substr(0,nPos);
// 
	char cTmp[200] = "";
	sprintf(cTmp,"%d_%s",m_pSrvParam->pClientCfg->n_cli_id,m_pSrvParam->pClientCfg->strCliName.c_str());
	
	m_strOffLineDataPath += FILE_PATH_OPT_STR + strMoudleName + FILE_PATH_OPT_STR + "OfflineData" + FILE_PATH_OPT_STR + cTmp + FILE_PATH_OPT_STR;
	m_tFirstOffLineEvent = 0;
}

/**
* @brief         发送事件消息到系统内部
* @param[in]     NX_EVENT_MESSAGE & Msg :事件消息结构
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::SendEventMsg( IN NX_EVENT_MESSAGE & Msg)
{
	RcdErrLogWithParentClass("SendEventMsg():服务端消息业务不支持发送事件信息","CNXEcSrvMsgOperaObj");
	return 0;
}

/**
* @brief         发送通用消息到系统内部
* @param[in]     NX_COMMON_MESSAGE & Msg :通用消息结构
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::SendCommonMsg( IN NX_COMMON_MESSAGE & Msg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE ResultMsg;
	switch(Msg.n_msg_type)
	{
	case NX_IED_CALL_HARDSTRAP_ASK:   // 召唤开关量
		if( m_pSrvParam->pClientCfg->n_di_source == 0 )  // 从数据库获取
		{
			sprintf(cError,"收到召唤开关量命令:%s,本客户端配置为从数据库获取",_get_commonmsg_desc(Msg).c_str());
			RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
			m_CmdDeque.push_back(Msg);
			return 0;
		}
		break;
	case NX_IED_CTRL_SG_CHECK_ASK:                  // 控制类
	case NX_IED_CTRL_SG_EXC_ASK:
	case NX_IED_CTRL_SGZONE_CHECK_ASK:
	case NX_IED_CTRL_SGZONE_EXC_ASK:
	case NX_IED_CTRL_SOFTSTRAP_CHECK_ASK:
	case NX_IED_CTRL_SOFTSTRAP_EXC_ASK:
	case NX_IED_CTRL_IEDTRIP_REST_ASK:
	case NX_IED_CTRL_IEDREMOTE_TRIP_ASK:
	case NX_IED_CTRL_SET_TIME_ASK:
	case NX_IED_CTRL_61850SRV_WRITE_ASK:
		sprintf(cError,"收到控制命令:%s",_get_commonmsg_desc(Msg).c_str());
		RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return __CtrlCmdHandle(Msg);
	default:
		break;
	}

	if( m_pObserver == NULL )
	{
		RcdErrLogWithParentClass("SendCommonMsg():观察者对象为NULL,无法发送命令到总线","CNXEcSrvMsgOperaObj");
		// 生成失败结果
		_make_commonmsg_failed_response(Msg,ResultMsg);
		// 结果返回规约库
		__SendCallResultToProObj(ResultMsg);
		ResultMsg.list_subfields.clear();
		return 0;
	}

	sprintf(cError,"收到召唤命令:%s,通过观察者发送到总线",_get_commonmsg_desc(Msg).c_str());
	RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

	int nRet = m_pObserver->SendCommand(Msg);
	if( 0 != nRet)
	{
		// 生成失败结果
		_make_commonmsg_failed_response(Msg,ResultMsg);
		// 结果返回规约库
		__SendCallResultToProObj(ResultMsg);
		ResultMsg.list_subfields.clear();
		return 0;
	}
	return nRet;
}

/**
* @brief         控制命令处理
* @param[in]     NX_COMMON_MESSAGE & Msg:控制命令
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__CtrlCmdHandle(IN NX_COMMON_MESSAGE & Msg)
{
	char cError[255]="";
	NX_COMMON_MESSAGE ResultMsg;
	bool	bIsUseYk		 = false;
	int		nYkStrapStatus	 = 0;
	//Add by songliang 20150320
    CYKStrapRead myYkObj;
	if (myYkObj.YKStrapObjInit())
	{
		bIsUseYk = true;
	}

	nYkStrapStatus = myYkObj.GetYKStrapStatus();
	
	if (bIsUseYk && (nYkStrapStatus == 0))
	{
		RcdErrLogWithParentClass("__CtrlCmdHandle():系统启用了远控压板,而且远控压板的状态为OFF,不允许执行远控操作.","CNXEcSrvMsgOperaObj");
		// 生成失败结果
		_make_commonmsg_failed_response(Msg,ResultMsg);
		// 结果返回规约库
		__SendCallResultToProObj(ResultMsg);
		ResultMsg.list_subfields.clear();
		return 0;
	}
	//Add by songliang end.
	if( (!bIsUseYk) && (!m_pSrvParam->pClientCfg->b_ctrlenable) ) // 不允许远控  Add by songliang : (nIsUseYkStrap != 1) && 
	{
		sprintf(cError,"__CtrlCmdHandle():客户端(name=%s channelName=%s)系统未启了远控压板,但数据库中设置为不允许远控,命令:%s 执行失败",
			    m_pSrvParam->pClientCfg->strCliName.c_str(),m_pSrvParam->pClientCfg->strChannelName.c_str(),
			    _get_commonmsg_desc(Msg).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

		// 生成失败结果
		_make_commonmsg_failed_response(Msg,ResultMsg);
		// 结果返回规约库
		__SendCallResultToProObj(ResultMsg);
	    ResultMsg.list_subfields.clear();
		return 0;
	}

	// 对时命令的处理
	if( Msg.n_msg_type == NX_IED_CTRL_SET_TIME_ASK )
	{
		return __SetTimeCmdHandle(Msg);
	}

	// 发送到总线
	if( m_pObserver == NULL )
	{
		RcdErrLogWithParentClass("__CtrlCmdHandle():观察者对象为NULL,无法发送控制命令到总线","CNXEcSrvMsgOperaObj");
		return -1;
	}
	int nRet = m_pObserver->SendCommand(Msg);
	if( 0 != nRet)
	{
		// 生成失败结果
		_make_commonmsg_failed_response(Msg,ResultMsg);
		// 结果返回规约库
		__SendCallResultToProObj(ResultMsg);
		ResultMsg.list_subfields.clear();
		return 0;
	}
	return nRet;
}

/**
* @brief         对时命令处理
* @param[in]     NX_COMMON_MESSAGE & Msg:对时命令
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__SetTimeCmdHandle(IN NX_COMMON_MESSAGE & Msg)
{
	char cError[255]="";
	CTimeConvert TmCvt;
	MY_TIME_INFO UpdateTm;
	string strUpdateTm = "";
	NX_COMMON_MSG_LIST SetTimeCmdList;

	// 获得要修改的时间
	TmCvt.SetTime(Msg.n_result,Msg.n_backup );   // n_result和n_backup分别为秒和毫秒
	TmCvt.GetTimeOfMyTime(UpdateTm);
	TmCvt.GetStandStringTime(strUpdateTm);
	switch(m_pSrvParam->pClientCfg->n_modify_time)
	{
		case 1:    // 校正主机 修改本地时间
			sy_update_localtime(&UpdateTm);
			sprintf(cError,"__SetTimeCmdHandle():修改本地时间为:%s.%d",strUpdateTm.c_str(),TmCvt.GetMilSecond());
			break;
		case 2:    // 校正装置  为除子站外的各装置生成对时命令并下发
			__MakeSetDevTimeCmd(Msg.n_result,Msg.n_backup,SetTimeCmdList);
			sprintf(cError,"__SetTimeCmdHandle():向所有ied发送对时命令,时间:%s.%d",strUpdateTm.c_str(),TmCvt.GetMilSecond());
			break;
		case 3:    // 主机和装置时间同时校正
			sy_update_localtime(&UpdateTm);
			__MakeSetDevTimeCmd(Msg.n_result,Msg.n_backup,SetTimeCmdList);
			sprintf(cError,"__SetTimeCmdHandle():修改本地时间并向所有ied发送对时命令,时间:%s.%d",strUpdateTm.c_str(),TmCvt.GetMilSecond());
			break;
		case 0:    // 不处理
		default:
			sprintf(cError,"__SetTimeCmdHandle():客户端(name=%s channelName=%s)不允许对时,对时命令不予处理",
				    m_pSrvParam->pClientCfg->strCliName.c_str(),m_pSrvParam->pClientCfg->strChannelName.c_str());
			RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
			return 0;
	}

	RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

	// 通过观察者发送命令
	NX_COMMON_MESSAGE CmdMsg;
	while( SetTimeCmdList.size() > 0 )
	{
		CmdMsg = SetTimeCmdList.front();
		// 发送到总线
		if( m_pObserver != NULL )
			m_pObserver->SendCommand(CmdMsg);
	    
		CmdMsg.list_subfields.clear();
		SetTimeCmdList.pop_front();
	}
	
	// 回送规约库处理成功结果
	NX_COMMON_MESSAGE ResultMsg;
	_make_commonmsg_failed_response(Msg,ResultMsg);
	ResultMsg.n_result = MB_COMMAND_OPRATION_RESULT_SUCCEED;
	__SendCallResultToProObj(ResultMsg);

	return 0;
}

/**
* @brief         生成各装置的对时命令
* @param[in]     int nUtcSecond:秒
* @param[in]     int nMilSec:毫秒
* @param[out]     NX_COMMON_MSG_LIST& CmdList:保存对时命令列表
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__MakeSetDevTimeCmd(IN int nUtcSecond,IN int nMilSec,OUT NX_COMMON_MSG_LIST& CmdList)
{
	char cError[255]="";
	NX_COMMON_MESSAGE CmdMsg;
	EC_IED_BASIC_LIST IedList;
	const IED_TB * pIed = NULL;

	// 获得所有IED配置
	m_pModelSeekIns->GetIedBasicList(IedList);
	while( IedList.size() > 0 )
	{
		pIed = IedList.front();
		if( pIed->e_psrtype != SUB_STATION )   // 子站类型设备不处理
		{
			CmdMsg.n_msg_type  = NX_IED_CTRL_SET_TIME_ASK;
			CmdMsg.n_msg_topic = NX_TOPIC_COMMAND;
			CmdMsg.n_obj_id    = pIed->n_obj_id;
			CmdMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
			CmdMsg.n_data_src  = 2; //远方主站
			CmdMsg.n_result    = nUtcSecond;
			CmdMsg.n_backup    = nMilSec;
			CmdList.push_back(CmdMsg);
		}
		
		IedList.pop_front();
		pIed = NULL;
	}
	sprintf(cError,"__MakeSetDevTimeCmd():生成%d条设备对时命令",CmdList.size());
	RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
	return 0;
}

/**
* @brief         初始化注册对象(观察者或目标者)
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::__InitRegisterObj()
{
	if( ( m_pSrvParam == NULL ) || (m_pSrvParam->pClientCfg == NULL ) )
	{
		RcdErrLogWithParentClass("__InitRegisterObj():服务端业务参数为NULL,无法初始化注册对象","CNXEcSrvMsgOperaObj");
		return -1;
	}

	return __InitObserver();
}

/**
* @brief         释放注册对象(观察者或目标者)
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::__FreeRegisterObj()
{
	return __FreeObserver();
}

/**
* @brief         将要开辟的各线程信息初始化后加入线程队列
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::__InitThreadInfo()
{
	//增加事件过滤处理线程
	EC_THREAD_INFO * pThreadInfo = new EC_THREAD_INFO;
	if( pThreadInfo == NULL)
	{
		RcdErrLogWithParentClass("为事件过滤处理线程分配线程信息内存失败,无法初始化","CNXEcSrvMsgOperaObj");
		return -1;
	}
	pThreadInfo->pSelfObj         = this;
	pThreadInfo->strThreadDes     ="事件过滤处理__EventFilterLoop()";
	pThreadInfo->pCallBackFunc    =__OnEventFilterThreadExec;
	AddThreadInfo(pThreadInfo);
	pThreadInfo = NULL;


	// 增加命令处理线程
	pThreadInfo = new EC_THREAD_INFO;
	if( pThreadInfo == NULL)
	{
		RcdErrLogWithParentClass("为命令处理线程分配线程信息内存失败,无法初始化","CNXEcSrvMsgOperaObj");
		return false;
	}
	pThreadInfo->pSelfObj         = this;
	pThreadInfo->strThreadDes     ="命令处理__CmdLoop()";
	pThreadInfo->pCallBackFunc    =__OnCmdThreadExec;
	AddThreadInfo(pThreadInfo);
	pThreadInfo = NULL;

	return 0;
}

/** 
* @brief         恢复与规约库的正常数据交换后的后续处理
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcSrvMsgOperaObj::__RestoreDataExchangeHandle()
{
	// 读取暂停期间保存到硬盘的事件信息
	m_tFirstOffLineEvent = 0;
	__ReadEventFromDisk();

	return true;
}

/**
* @brief         观察者对象资源分派，设置注册信息等
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::__InitObserver()
{
	if( m_pObserver != NULL )
	{
		RecordErrorLog("__InitRegisterObj()时观察者已经初始化，不再初始化");
		return 0;
	}

	// 分配资源(用唯一的通道编号作为观察者ID)
	m_pObserver = new CNXObserver(m_pLogRecord,m_pSrvParam->pClientCfg->n_channel_id);
	if( NULL == m_pObserver )
	{
		RecordErrorLog("__InitRegisterObj()时为观察者分配资源失败");
		return COMMONERR_NEW_MEM_FAILED;
	}

	// 设置对象描述
// 	char cDes[255]="";
// 	sprintf(cDes,"%s->%s",m_pSrvParam->pClientCfg->strCliName.c_str(),m_pSrvParam->pClientCfg->strChannelName.c_str());
	m_pObserver->SetRegObjDesc(SHARE_LIB_MSG_OPERATION);

	// 设置不关注事件信息
	EC_INFO_ORDER_LIST  NotOrderEventList;
	__InitNotOrderEventList(NotOrderEventList);
	m_pObserver->SetCareEventType(false,NotOrderEventList);
	NotOrderEventList.clear();

	// 设置不关注的设备
	EC_DEV_ORDER_LIST NotOrderDevList;
	__InitNotOrderDevList(NotOrderDevList);
	m_pObserver->SetCareDev(false,NotOrderDevList); 
	NotOrderDevList.clear();

	// 设置处理回调
	m_pObserver->SetResultCallBack(this,__OnCallResultRecv);
	m_pObserver->SetEventCallBack(this,__OnEventRecv);

	// 注册到服务中介
	if( !m_pObserver->Init() )
	{
		return -1;
	}
	return 0;
}

/**
* @brief         召唤结果信息接收处理(由观察者回调)
* @param[in]     LPVOID pRegObj：注册对象
* @param[in]     NX_COMMON_MESSAGE & ResultMsg:收到的结果信息
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::__OnCallResultRecv(LPVOID pRegObj,NX_COMMON_MESSAGE & ResultMsg)
{
	// 从总线收到召唤结果后直接发送给规约对象
	if( pRegObj == NULL )
		return -1;
	CNXEcSrvMsgOperaObj * pMsgOpera = (CNXEcSrvMsgOperaObj*) pRegObj;

	return pMsgOpera->__SendCallResultToProObj(ResultMsg);
}

/**
* @brief         自动上送事件信息接收处理(由观察者回调)
* @param[in]     LPVOID pRegObj：注册对象
* @param[in]     NX_EVENT_MESSAGE & EventMsg:收到的事件信息
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::__OnEventRecv(LPVOID pRegObj,NX_EVENT_MESSAGE & EventMsg)
{
	char cError[255]="";
	// 从总线收到事件信息后，加入事件队列或保存到磁盘
	if( pRegObj == NULL )
		return -1;

	CNXEcSrvMsgOperaObj * pMsgOpera = (CNXEcSrvMsgOperaObj*) pRegObj;

	//2024/8/9 发现主站重连后，解析离线文件，会重复发送数据，定位问题为通道问题（与主站连接的通道不是最大通道，导致一个通道走了else,而最大通道存了文件）
	//if( pMsgOpera->m_bPauseOpera && pMsgOpera->m_bEnd )
	if( pMsgOpera->m_bPauseOpera)// 与规约对象暂停交换数据且各项线程业务未启动
	{
		pMsgOpera->__SaveEventToDisk(EventMsg);
	}
	else
	{
		while( pMsgOpera->m_EventDeque.size() >= MAX_EVENT_DEQUE_LEN )
		{
			pMsgOpera->m_EventDeque.pop_front();
			sprintf(cError,"__OnEventRecv():事件队列中元素个数=%d,超过最大长度:%d,删除头元素",
				    pMsgOpera->m_EventDeque.size(),MAX_EVENT_DEQUE_LEN);
			pMsgOpera->RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		}
		if( pMsgOpera->m_bPauseOpera ) // 对端有连接过，可能短期中断连接情况下,内存中保留除通信状态外的其它信息
		{
			if( EventMsg.n_msg_type == NX_IED_EVENT_COMMU_REPORT )
				return 0;
		}

		pMsgOpera->m_EventDeque.push_back(EventMsg);
	}

	return 0;
}

/**
* @brief         释放观察者信息资源，从服务中介注销
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::__FreeObserver()
{
	if( m_pObserver != NULL )
	{
		m_pObserver->Exit();

		delete m_pObserver;

		m_pObserver = NULL;

		RcdTrcLogWithParentClass("从服务终结注销观察者对象","CNXEcSrvMsgOperaObj");
	}

	return 0;
}

/**
* @brief         事件过滤处理线程回调执行函数
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     LPVOID pParam: 参数
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__OnEventFilterThreadExec(LPVOID pObj,LPVOID  pParam)
{
	char cError[255]    = "";

	if( NULL == pObj )
		return THREAD_RET_VALUE;

	CNXEcSrvMsgOperaObj * pMsgOpera = (CNXEcSrvMsgOperaObj *)pObj;

	try
	{
		pMsgOpera->__EventFilterLoop();
	}
	catch(...)
	{
		sprintf(cError,"事件过滤处理线程异常退出,原因:%s:(%d)",strerror(errno),errno);
		pMsgOpera->RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

		return THREAD_RET_VALUE;
	}
	return THREAD_RET_VALUE;
}

/**
* @brief         事件过滤处理循环
* @param[in]     无
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__EventFilterLoop()
{
	NX_EVENT_MESSAGE EventMsg;
	while( !m_bEnd )
	{
		// 如果与规约库暂停交换数据，且事件队列长度低于阀值的50%
		if( ( m_bPauseOpera) && (m_EventDeque.size() < MAX_EVENT_DEQUE_LEN*0.5) )
		{
			sy_sleep(50);
			continue;
		}

		// 发送关联合并事件
		__SendMergedEventHandle();

		if( m_EventDeque.empty() )
		{
			sy_sleep(50);
			continue;
		}

		EventMsg = m_EventDeque.front();
		if( m_bPauseOpera )
		{
			__SaveEventToDisk(EventMsg);
		}
		else
		{
			// 过滤处理
			if( __EventFilterHandle(EventMsg) == 0 )
			{
				// 交给规约对象
				__SendEventToProObj(EventMsg);
			}
		}
		
		EventMsg.list_subfields.clear();
		// 删除元素
		m_EventDeque.pop_front();
	}

	RcdTrcLogWithParentClass("__EventFilterLoop()正常退出","CNXEcSrvMsgOperaObj");

	return 0;
}

/**
* @brief         指定事件的过滤处理
* @param[in]     NX_EVENT_MESSAGE &EventMsg:指定的事件信息
* @param[in]     无
* @return        int: 0-需要发送 其它：无需发送
*/
int CNXEcSrvMsgOperaObj::__EventFilterHandle(IN NX_EVENT_MESSAGE &EventMsg)
{
	char cError[255]="";

	// 检修信息过滤处理
	if( !__DebugInfoFilter(EventMsg) )
	{
		sprintf(cError,"__EventFilterHandle():事件:%s由于设置不上送检修信息被过滤,丢弃",
			    _get_eventmsg_desc(EventMsg).c_str());
		RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return -1;
	}

	// 动作事件等待关联(对于部分规约故障类型必须和事件关联才能上送的,需要做关联处理)
	if( EventMsg.n_msg_type ==  NX_IED_EVENT_EVENT_REPORT )
	{
		if( __IsNeedMerge(EventMsg) )  // 合并的事件暂无需发送,待合并完再发送
			return -1;
	}

	// 告警、动作需按信息级别过滤处理
	if( ( EventMsg.n_msg_type == NX_IED_EVENT_ALARM_REPORT ) || ( EventMsg.n_msg_type ==  NX_IED_EVENT_EVENT_REPORT ) )
	{
		if( !__InfoLevelFilter(EventMsg) )
		{
			sprintf(cError,"__EventFilterHandle():事件:%s由于设置指定信息级别的事件不上送被过滤,丢弃",
				_get_eventmsg_desc(EventMsg).c_str());
			RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
			return -1;
		}
	}

	return 0;
}

/**
* @brief         是否需要关联合并处理
* @param[in]     NX_EVENT_MESSAGE& EventMsg:事件信息
* @return        bool: true-需要合并 false：不需要合并
*/
bool CNXEcSrvMsgOperaObj::__IsNeedMerge(IN NX_EVENT_MESSAGE & EventMsg)
{
	char cError[512]="";
	if( m_pSrvParam->pClientCfg->nReaserve <= 0 )// 无需合并
		return false;

	if( EventMsg.list_subfields.size() <= 0 ) // 子集个数小于等于0,无需合并
		return false;

	sprintf(cError,"IsNeedMerge():动作事件(%s)需要等待合并,等待时间=%d秒",
		    _get_eventmsg_desc(EventMsg).c_str(),m_pSrvParam->pClientCfg->nReaserve);
	RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

	// 锁定合并事件映射表
	CAutoLockOnStack tmpLock(&m_LockForWaitEventMap);

	// 查找指定设备编号的相关事件是否存在
	EC_IED2WAITEVENTMAP::iterator iteMap = m_IedToWaitEventMap.find(EventMsg.n_event_obj);
	if( iteMap == m_IedToWaitEventMap.end() )
	{
		// 第一条新建
		WAIT_EVENT_INFO tmpWaitInfo;
		tmpWaitInfo.nRcvTime = time(NULL);
		tmpWaitInfo.nEventTime = EventMsg.list_subfields[0].n_curvalueutctm;  // 取第一条事件的时间
		tmpWaitInfo.nMilSecond = EventMsg.list_subfields[0].n_curms;
		tmpWaitInfo.EventMsg = EventMsg;

		WAIT_EVENT_INFO_LIST * pMergeList = new WAIT_EVENT_INFO_LIST;
		if( pMergeList == NULL )
			return false;
		pMergeList->push_back(tmpWaitInfo);
		// 加入映射表
		m_IedToWaitEventMap[EventMsg.n_event_obj] = pMergeList;

		// 清除临时资源
		tmpWaitInfo.EventMsg.list_subfields.clear();
		return true;
	}
	// 合并处理
	WAIT_EVENT_INFO_LIST * pMergeList = iteMap->second;
	if( pMergeList == NULL )
		return false;
	
	// 同一次故障时间
	int nFaultTime = m_pSrvParam->pClientCfg->nReaserve % 15; 
	if( nFaultTime <= 0 )
		nFaultTime = 15;   // 默认按15秒计

	WAIT_EVENT_INFO_LIST::iterator ite = pMergeList->begin();
	while( ite != pMergeList->end() )
	{
		if( abs( int(ite->nEventTime - EventMsg.list_subfields[0].n_curvalueutctm) ) <= nFaultTime )
		{
			ite->EventMsg.list_subfields.insert(ite->EventMsg.list_subfields.end(),
				                                EventMsg.list_subfields.begin(),
				                                EventMsg.list_subfields.end());
			_ZERO_MEM(cError,512);
			sprintf(cError,"IsNeedMerge():event_obj=%d的新动作事件合并到已有事件成功,合并后子集个数=%d",
				    EventMsg.n_event_obj,ite->EventMsg.list_subfields.size());
			RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
			return true;
		}
		++ite;
	}

	// 如果在已有链表中的事件不符合合并条件，则新建等待后续事件来临后合并
	WAIT_EVENT_INFO tmpWaitInfo;
	tmpWaitInfo.nRcvTime = time(NULL);
	tmpWaitInfo.nEventTime = EventMsg.list_subfields[0].n_curvalueutctm;  // 取第一条事件的时间
	tmpWaitInfo.nMilSecond = EventMsg.list_subfields[0].n_curms;
	tmpWaitInfo.EventMsg = EventMsg;
	pMergeList->push_back(tmpWaitInfo);
	// 清除临时资源
	tmpWaitInfo.EventMsg.list_subfields.clear();
	return true;
}

/**
* @brief         发送关联合并事件相关处理
* @param[in]     无
* @return        int 0-成功 其它-失败
*/
int CNXEcSrvMsgOperaObj::__SendMergedEventHandle()
{
	char cError[512]="";
	if( m_pSrvParam->pClientCfg->nReaserve <= 0 )// 无需合并
		return 0;

	WAIT_EVENT_INFO_LIST * pMergeList = NULL;
	WAIT_EVENT_INFO_LIST::iterator iteList;
	time_t nCurTime = 0;

	// 锁定合并事件映射表
	CAutoLockOnStack tmpLock(&m_LockForWaitEventMap);

	EC_IED2WAITEVENTMAP::iterator iteMap = m_IedToWaitEventMap.begin();
	while( iteMap != m_IedToWaitEventMap.end() )
	{
		pMergeList = iteMap->second;
		if( pMergeList == NULL )
		{
			m_IedToWaitEventMap.erase(iteMap++); // 防止迭代器失效
			continue;
		}

		// 遍历链表
		iteList = pMergeList->begin();
		nCurTime = time(NULL);
		while(iteList != pMergeList->end() )
		{
			// 判断是否到达等待的最长时间
			if( (nCurTime - iteList->nRcvTime) >= m_pSrvParam->pClientCfg->nReaserve )
			{
				_ZERO_MEM(cError,512);
				if( !__InfoLevelFilter(iteList->EventMsg) )
				{
					sprintf(cError,"__SendMergedEventHandle():event_obj=%d 的动作事件由于设置指定信息级别的事件不上送被过滤,丢弃",
						    iteList->EventMsg.n_event_obj);
					RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
				}
				else
				{
					// 发送事件给规约库
					__SendEventToProObj(iteList->EventMsg);
					sprintf(cError,"__SendMergedEventHandle():event_obj=%d的动作事件(子集个数=%d)等待合并完毕发给规约库,开始等待时间=%d,当前时间=%d",
						    iteList->EventMsg.n_event_obj,iteList->EventMsg.list_subfields.size(),iteList->nRcvTime,nCurTime);
					RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
				}
				iteList->EventMsg.list_subfields.clear();
				iteList = pMergeList->erase(iteList);       // 发送完毕后,删除该结点,迭代器删除防止失效
			}
			else
				++iteList;
		}

		// 判断该链表内是否有数据
		if( pMergeList->size() <= 0 )
		{
			delete pMergeList;
			pMergeList = NULL;
			m_IedToWaitEventMap.erase(iteMap++); // 防止迭代器失效
			continue;
		}
        
		++iteMap;
		pMergeList = NULL;
	}
	return 0;
}

/**
* @brief         指定事件的检修过滤处理
* @param[in]     NX_EVENT_MESSAGE &EventMsg:指定的事件信息
* @return        bool: true-需要发送 false：无需发送
*/
bool CNXEcSrvMsgOperaObj::__DebugInfoFilter(IN NX_EVENT_MESSAGE &EventMsg)
{
	char cError[255]="";
	EC_STATUS_INFO tmpStatus;
	NX_EVENT_MSG_SUBFILED_LIST::iterator ite;

	// 如果是运行状态变位、配置变化及资源监视信息直接上送
	if( ( EventMsg.n_msg_type == NX_SYS_EVENT_IED_RUNSTATUS_REPORT ) ||
		( EventMsg.n_msg_type == NX_SYS_EVENT_CONFIG_ALARM )         ||
		( EventMsg.n_msg_type == NX_SYS_EVENT_SYS_INFO_REPORT) 
	  )
		return true;

	// 获取设备运行状态
	tmpStatus.eDevType = (DEV_CATEGORY)EventMsg.n_obj_type;
	tmpStatus.nDevID   = EventMsg.n_event_obj;
	tmpStatus.eStatusType = EC_RUN_STATUS;
	if( !m_pModelSeekIns->GetDevStatusInfo(tmpStatus) )   
	{
		sprintf(cError,"__DebugInfoFilter():处理事件:%s时,获取设备的运行状态失败,全部按正常信息上送",
			    _get_eventmsg_desc(EventMsg).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return true;
	}

	// 如果设备运行状态不为检修和调试态，根据各信息点的品质上送
	if( (tmpStatus.nStatus != 0) && (tmpStatus.nStatus != 4) )
	{
		if( m_pSrvParam->pClientCfg->bSndDebugInf )
			return true;

		ite = EventMsg.list_subfields.begin();
		while ( ite != EventMsg.list_subfields.end() )
		{
			if( ite->n_quality == 2 )  // 1：运行  2：检修  3：无效数据
			{
				if( !m_pSrvParam->pClientCfg->bSndDebugInf )  // 不上送，则删除该结点
				{
					_ZERO_MEM(cError,255);
					sprintf(cError,"__DebugInfoFilter():事件:%s中,id =%d 的信息点,Quality=%d,检修信息被过滤,不上送",
						    _get_eventmsg_desc(EventMsg).c_str(),ite->n_sub_sub_obj_id,ite->n_quality);
					ite = EventMsg.list_subfields.erase(ite);  // 删除结点同时迭代器后移
					RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
					continue;
				}
			}
			++ite;
		}
		if( EventMsg.list_subfields.size() > 0 )
			return true;
		else
			return false;
	}

	// 判断上送调试信息标识
	if( !m_pSrvParam->pClientCfg->bSndDebugInf )
		return false;  // 不上送设备的所有信息

	// 设备为检修状态且调试信息允许上送时，需要设置各事件的品质
	ite = EventMsg.list_subfields.begin();
	while ( ite != EventMsg.list_subfields.end() )
	{
		ite->n_quality = 2;   // 1：运行  2：检修  3：无效数据
		++ite;
	}
	return true;
}

/**
* @brief         指定事件的信息级别过滤处理
* @param[in]     NX_EVENT_MESSAGE &EventMsg:指定的事件信息
* @return         bool: true-需要发送 false：无需发送
*/
bool CNXEcSrvMsgOperaObj::__InfoLevelFilter(IN NX_EVENT_MESSAGE &EventMsg)
{
	char cError[255]="";
	bool bSend = false;

	NX_EVENT_MSG_SUBFILED_LIST::iterator ite = EventMsg.list_subfields.begin();
	while ( ite != EventMsg.list_subfields.end() )
	{
		if( EventMsg.n_msg_type == NX_IED_EVENT_ALARM_REPORT )
			bSend = __IsIedAlarmPointSend(*ite);
		else if( EventMsg.n_msg_type == NX_IED_EVENT_EVENT_REPORT )
		{
			bSend = __IsIedEventPointSend(*ite);
		}
		else
			bSend = true;

		// 如果整条事件中，有一个点需要上送则该条信息上送
		if( bSend )
			break;

		++ite;
	}
	return bSend;
}

/**
* @brief         根据信息级别判断指定告警事件信息点是否上送
* @param[in]     NX_EVENT_FIELD_STRUCT & EventField:指定的告警点信息
* @return         bool: true-需要发送 false：无需发送
*/
bool CNXEcSrvMsgOperaObj::__IsIedAlarmPointSend(IN NX_EVENT_FIELD_STRUCT & EventField)
{
	char cError[255]="";
	// 获得指定信息点的告警配置
	const ALARM_TB * pTb = m_pModelSeekIns->GetIedAlarmCfg(EventField.n_obj_id,EventField.n_sub_obj_id,EventField.n_sub_sub_obj_id);
    if( pTb == NULL )
	{
		sprintf(cError,"__IsIedAlarmPointSend():获取ied id=%d ld=%d alarmId=%d的配置失败,默认上送",
			    EventField.n_obj_id,EventField.n_sub_obj_id,EventField.n_sub_sub_obj_id);
		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return true;
	}

	// 判断信息点的级别配置是否有效(最多1-32级)
	if( ( pTb->n_psrgradetype <= 0 ) || ( pTb->n_psrgradetype > 32 ) )
	{
// 		sprintf(cError,"__IsIedAlarmPointSend():ied id=%d ld=%d alarmId=%d的信息级别为:%d,无效值,默认上送",
// 			   EventField.n_obj_id,EventField.n_sub_obj_id,EventField.n_sub_sub_obj_id,pTb->n_psrgradetype);
// 		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return true;
	}

	// 判断该级别信息是否上送 (无配置，默认上送)
	if( m_pClientSendAlarmCfg == NULL )
		return true;

	// 第(n)级数据的上送标志在第n-1位
	int nBitPos = 1 << (pTb->n_psrgradetype-1) ; 
	if( ( m_pClientSendAlarmCfg->n_order_degree & nBitPos ) != 1 )
		return false;

	return true;
}

/**
* @brief         根据信息级别判断指定动作事件信息点是否上送
* @param[in]     NX_EVENT_FIELD_STRUCT & EventField:指定的动作点信息
* @return         bool: true-需要发送 false：无需发送
*/
bool CNXEcSrvMsgOperaObj::__IsIedEventPointSend(IN NX_EVENT_FIELD_STRUCT & EventField)
{
	char cError[255]="";
	if( EventField.n_field_type == 1 )  // 故障参数　无需判断级别,直接上送
	{
		return true;
	}

	// 获得指定信息点的动作配置
	const EVENT_TB * pTb = m_pModelSeekIns->GetIedEventCfg(EventField.n_obj_id,EventField.n_sub_obj_id,EventField.n_sub_sub_obj_id);
	if( pTb == NULL )
	{
		sprintf(cError,"__IsIedEventPointSend():获取ied id=%d ld=%d eventId=%d的配置失败,默认上送",
			    EventField.n_obj_id,EventField.n_sub_obj_id,EventField.n_sub_sub_obj_id);
		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return true;
	}

	// 判断信息点的级别配置是否有效(最多1-32级)
	if( ( pTb->n_psrgradetype <= 0 ) || ( pTb->n_psrgradetype > 32 ) )
	{
// 		sprintf(cError,"__IsIedEventPointSend():ied id=%d ld=%d eventId=%d的信息级别为:%d,无效值,默认上送",
// 			    EventField.n_obj_id,EventField.n_sub_obj_id,EventField.n_sub_sub_obj_id,pTb->n_psrgradetype);
// 		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return true;
	}

	// 判断该级别信息是否上送 (无配置，默认上送)
	if( m_pClientSendEventCfg == NULL )
		return true;

	// 第(n)级数据的上送标志在第n-1位
	int nBitPos = 1 << (pTb->n_psrgradetype-1) ; 

	if( ( m_pClientSendEventCfg->n_order_degree & nBitPos ) != 1 )
		return false;

	return true;
}

/**
* @brief         命令处理线程回调执行函数
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     LPVOID pParam: 参数
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__OnCmdThreadExec(LPVOID pObj,LPVOID  pParam)
{
	char cError[255]    = "";

	if( NULL == pObj )
		return THREAD_RET_VALUE;

	CNXEcSrvMsgOperaObj * pMsgOpera = (CNXEcSrvMsgOperaObj *)pObj;

	try
	{
		pMsgOpera->__CmdLoop();
	}
	catch(...)
	{
		sprintf(cError,"命令处理线程异常退出,原因:%s:(%d)",strerror(errno),errno);
		pMsgOpera->RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

		return THREAD_RET_VALUE;
	}
	return THREAD_RET_VALUE;
}

/**
* @brief         命令处理循环
* @param[in]     无
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__CmdLoop()
{
	char cError[255]="";
	NX_COMMON_MESSAGE CmdMsg;
	while( !m_bEnd )
	{
		if( m_CmdDeque.empty() )
		{
			sy_sleep(100);
			continue;
		}

		CmdMsg = m_CmdDeque.front();

		if( !m_bPauseOpera )
		{
			// 命令处理
			__CallCmdHandle(CmdMsg);
		}
		else
		{
			sprintf(cError,"__CmdLoop():与规约对象已暂停数据交换,命令:%s丢弃不予处理",
				    _get_commonmsg_desc(CmdMsg).c_str());
			RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		}

		CmdMsg.list_subfields.clear();

		// 删除元素
		m_CmdDeque.pop_front();
	}

	RcdTrcLogWithParentClass("__CmdLoop()正常退出","CNXEcSrvMsgOperaObj");
	return 0;
}

/**
* @brief         召唤命令处理
* @param[in]     NX_COMMON_MESSAGE & Msg:召唤命令
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__CallCmdHandle(IN NX_COMMON_MESSAGE & CmdMsg)
{
	char cError[255]="";
	int  nRet = -1;
	NX_COMMON_MESSAGE ResultMsg;

	switch(CmdMsg.n_msg_type )
	{
	case NX_IED_CALL_HARDSTRAP_ASK:     // 从数据库取设备开关量
		nRet = __GetIedHardStrapDataFromDb(CmdMsg,ResultMsg);
		break;
	default:
		sprintf(cError,"__CallCmdHandle():不支持msg_type=%d的消息处理,回复失败",CmdMsg.n_msg_type);
		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		break;
	}

	// 生成失败回应
	if( nRet != 0 )
	{
		_init_common_msg_struct(ResultMsg);
		_make_commonmsg_failed_response(CmdMsg,ResultMsg);
	}

	// 发送给规约对象
	__SendCallResultToProObj(ResultMsg);

	ResultMsg.list_subfields.clear();
	return 0;
}

/**
* @brief         从数据库获取设备的最新一次开关量值
* @param[in]     NX_COMMON_MESSAGE& CmdMsg:命令
* @param[out]    NX_COMMON_MESSAGE& ResultMsg:结果
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__GetIedHardStrapDataFromDb(IN NX_COMMON_MESSAGE& CmdMsg,OUT NX_COMMON_MESSAGE& ResultMsg)
{
	char cError[512]="";
	if( m_pModelSeekIns->GetModelMgrObj() == NULL )
		return -1;
	DB_OPER_PARAM SelectParam;       // 数据库查询参数
	CPlmRecordSet SelectResultSet;   // 数据库查询结果集
	char cValue[100] = "";
    
	// 设置查询表名称
	SelectParam.lst_tablename.push_back(chardstrp_tb);
	// 设置查询字段
	_add_db_field("strap_code",FD_TYPE_NUMERIC,SelectParam.lst_fddata);
	_add_db_field("ied_obj",FD_TYPE_NUMERIC,SelectParam.lst_fddata);
	_add_db_field("ld_code",FD_TYPE_NUMERIC,SelectParam.lst_fddata);
	_add_db_field("curvalue",FD_TYPE_NUMERIC,SelectParam.lst_fddata);
    
	// 设置查询条件
	// 条件1：ied_obj =  CmdMsg.n_obj_id 
	sprintf(cValue,"%d",CmdMsg.n_obj_id);
	_add_db_condition("ied_obj",CDT_TYPE_EQUAL,cValue,FD_TYPE_NUMERIC,CDT_LOGIAL_AND,SelectParam.lst_condition);
	// 条件2：ld_cod = CmdMsg.n_sub_obj_id
	_ZERO_MEM(cValue,100);
	sprintf(cValue,"%d",CmdMsg.n_sub_obj_id);
	_add_db_condition("ld_code",CDT_TYPE_EQUAL,cValue,FD_TYPE_NUMERIC,CDT_LOGIAL_AND,SelectParam.lst_condition);

	// 从数据库获取信息
	char cDes[255]="";
	if( m_pModelSeekIns->GetModelMgrObj()->dbm_select_records(&SelectParam,SelectResultSet,false,cDes) != 0)
	{
		_clear_db_opera_param(SelectParam);
		sprintf(cError,"__GetIedHardStrapDataFromDb():获取ied id=%d ld=%d的最新开关量值失败,原因:%s",
			    CmdMsg.n_obj_id,CmdMsg.n_sub_obj_id,cDes);
		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return -1;
	}

	_clear_db_opera_param(SelectParam);

	// 根据查询结果生成开关量召唤结果结构
	UINT nRecordNum = 0;
	SelectResultSet.get_record_num(nRecordNum);
	if ( nRecordNum <= 0 )
	{
		SelectResultSet.clear_result();
		sprintf(cError,"__GetIedHardStrapDataFromDb():获取ied id=%d ld=%d的最新开关量记录数为%d条",
			    CmdMsg.n_obj_id,CmdMsg.n_sub_obj_id,nRecordNum);
		RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		return -1;
	}

	// 设置结果结构头
	_copy_common_msg_head(CmdMsg,ResultMsg);
	ResultMsg.n_msg_type = NX_IED_CALL_HARDSTRAP_REP;
	ResultMsg.n_send_utctm = time(NULL);

	// 设置可变部分
	NX_COMMON_FIELD_STRUCT subFiled;
	SelectResultSet.move_to_top();      // 移到首记录
	int i= 0;
	string strStrapCode="",strValue="";
	for (i = 0; i< nRecordNum; i++ )
	{
		// 获得指定字段的值
		SelectResultSet.get_field_value(1,strStrapCode); // 按查询时添加的字段顺序号获取对应的值
		SelectResultSet.get_field_value(4,strValue);
// 		SelectResultSet.get_field_value_base_name("strap_code",strStrapCode);
// 		SelectResultSet.get_field_value_base_name("curvalue",strValue);

		subFiled.n_field_id = atoi(strStrapCode.c_str());
		subFiled.n_value    = atoi(strValue.c_str());
		if( subFiled.n_value < 0 )  // 数据库初始值可能为-1;
			subFiled.n_value = 0;

		// 结果加入列表
		ResultMsg.list_subfields.push_back(subFiled);
		strValue.clear();
		strStrapCode.clear();
		// 下一条
		SelectResultSet.move_to_next();
	}
    
	// 清空结果集
	SelectResultSet.clear_result();
	sprintf(cError,"__GetIedHardStrapDataFromDb():获取ied id=%d ld=%d的最新开关量值%d条并生成结果成功",
		    CmdMsg.n_obj_id,CmdMsg.n_sub_obj_id,nRecordNum);
	RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

	return 0;
}

/**
* @brief         发送召唤结果给规约对象
* @param[in]     NX_COMMON_MESSAGE & Msg:结果
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__SendCallResultToProObj(IN NX_COMMON_MESSAGE & Msg)
{
	char cError[255]="";
	if( ( m_pCommonMsgRcvCalBakObj != NULL ) && (m_pCommonMsgRcvCalBakFunc != NULL ) )
	{
		return m_pCommonMsgRcvCalBakFunc(m_pCommonMsgRcvCalBakObj,Msg);
	}
	
	sprintf(cError,"__SendCallResultToProObj():通用消息回调对象或回调函数没有注册,无法将结果:%s回复给规约对象",
		    _get_commonmsg_desc(Msg).c_str());

	RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
	
	return -1;
}

/**
* @brief         发送事件信息给规约对象
* @param[in]     NX_EVENT_MESSAGE & Msg:事件信息
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__SendEventToProObj(IN NX_EVENT_MESSAGE & Msg)
{
	char cError[255]="";
	if( ( m_pEventRcvCalBakFunc != NULL ) && (m_pEventRcvCalBakObj != NULL ) )
	{
		return m_pEventRcvCalBakFunc(m_pEventRcvCalBakObj,Msg);
	}

	sprintf(cError,"__SendEventToProObj():事件消息回调对象或回调函数没有注册,无法将事件:%s发送给规约对象",
		    _get_eventmsg_desc(Msg).c_str());

	RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

	return -1;
}

/**
* @brief         初始化客户端不订阅的事件列表及动作、告警的过滤配置信息
* @param[out]    EC_INFO_ORDER_LIST & NotOrderEventList:不订阅的事件列表
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__InitNotOrderEventList(OUT EC_INFO_ORDER_LIST & NotOrderEventList)
{
	char cError[255]="";
	if( m_pSrvParam->pMsgTypeOrderList == NULL )
		return -1;

	const LIST_ORDER * pOrder = m_pSrvParam->pMsgTypeOrderList;
	LIST_ORDER::const_iterator ite = pOrder->begin();
	while(ite != pOrder->end() )
	{
		if( !ite->b_order_flag )    // 不订阅
		{
			NotOrderEventList.push_back(ite->e_info_type);
			sprintf(cError,"__InitNotOrderEventList():客户端(%s)不订阅msg_type=%d的信息",
				    m_pSrvParam->pClientCfg->strCliName.c_str(),ite->e_info_type);
			RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		}
		else                       // 订阅
		{
			if( ite->e_info_type == NX_IED_EVENT_EVENT_REPORT )
				m_pClientSendEventCfg = &(*ite);
			else if( ite->e_info_type == NX_IED_EVENT_ALARM_REPORT )
				m_pClientSendAlarmCfg = &(*ite);
		}

		ite ++;
	}

	return 0;
}

/**
* @brief         初始化客户端不订阅的设备列表
* @param[out]    EC_DEV_ORDER_LIST & NotOrderDevList:不订阅的设备列表
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__InitNotOrderDevList(OUT EC_DEV_ORDER_LIST & NotOrderDevList)
{
	char cError[255]="";
	if( m_pSrvParam->pNotOrderDevList == NULL )
		return -1;

	DEV_UUID Dev;
	const LIST_NOT_ORDER * pOrder = m_pSrvParam->pNotOrderDevList;
	LIST_NOT_ORDER::const_iterator ite = pOrder->begin();
	while(ite != pOrder->end() )
	{
		Dev.eDevType = (DEV_CATEGORY)ite->n_eqm_type;    // 设备类型
		Dev.nDevID   = ite->n_eqm_obj;                   // 设备对象ID
		
		NotOrderDevList.push_back(Dev);
		sprintf(cError,"__InitNotOrderDevList():客户端(%s)不订阅type=%d id=%d的设备信息",
			    m_pSrvParam->pClientCfg->strCliName.c_str(),Dev.eDevType,Dev.nDevID);
		RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");

		ite ++;

		Dev.eDevType = (DEV_CATEGORY) 0;
		Dev.nDevID   = -1;
	}

	return 0;
}

/**
* @brief         网络中断期间，保存事件信息到磁盘
* @param[in]     NX_EVENT_MESSAGE & Msg:事件信息
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__SaveEventToDisk(IN NX_EVENT_MESSAGE &Msg)
{
	char cError[255]="";
	if( !m_pSrvParam->pClientCfg->bSaveIndisconn )
	{
		return -1;
	}
	
	if( (Msg.n_msg_type != NX_IED_EVENT_EVENT_REPORT) && (Msg.n_msg_type != NX_IED_EVENT_OSCFILE_REPORT))   
	    return -1;

	//如果当前通道所属客户端通信状态为通，也不保存事件信息到磁盘。
	bool bMyIsBiggest = true;
	if ( __GetClientStatus(bMyIsBiggest) == 1 ) //客户端状态为正常，说明有一个通道是通信正常的。不需要缓存事件。
	{
		return -1;
	}

	//如果客户端为断开，说明所有通道都是断开的，需要缓存事件，那么判断本通道编号是不是最大，最大者负责缓存
	if ( !bMyIsBiggest ) 
	{
		return -1;
	}

	printf("%d can record offline event.\n",m_pSrvParam->pClientCfg->n_channel_id);
	// 清理可能的过期报文记录文件：只保存近10天的断开报文。
	if ( m_tFirstOffLineEvent == 0 ) //记录第一次缓存事件到文件的时间（秒）。
	{
		m_tFirstOffLineEvent = time(NULL);
	}

	
// 	MY_TIME_INFO myTime;
// 	CTimeConvert CTimeCvt(time(NULL));
// 	CTimeCvt.GetTimeOfMyTime(myTime);
// 	int nDay = myTime.nDay;
 	char cFileName[200] = "";	
	

	if ( 0 != sy_dirfile_exist(m_strOffLineDataPath.c_str()))
	{
		int nRet = sy_create_dir((char *)(m_strOffLineDataPath.c_str()));
		if ( -1 == nRet)
		{
			sprintf(cError,"__SaveEventToDisk():网络中断期间保存事件时，创建文件保存路径失败:%s.",m_strOffLineDataPath.c_str());
			RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
			return -1;
		}
	}
	sprintf(cFileName,"%s%sofflinedata_%d.rcd",m_strOffLineDataPath.c_str(),FILE_PATH_OPT_STR,__GetFileSerialNo());
	//将事件结构体转成字符流。
	vector<u_int8>  vStream;
	__CvtEventMsgToStream(Msg,vStream);
	__WriteStream(vStream,cFileName);
	vStream.clear();

	sprintf(cError,"__SaveEventToDisk():网络中断期间保存事件消息:%s到磁盘成功",_get_eventmsg_desc(Msg).c_str());
	RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
	RcdErrLogWithParentClass(cFileName,"CNXEcSrvMsgOperaObj");
	return 0;
}

/**
* @brief         网络恢复后，从磁盘读取保存的事件信息到事件队列
* @param[in]     NX_EVENT_MESSAGE & Msg:事件信息
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvMsgOperaObj::__ReadEventFromDisk()
{
	//获取离线文件夹下的所有文件
	vector <_FILE_PROPERTY_INF> vFileInfo;
	char cErr[255] = "";

	if ( 0 != sy_get_files_property_indir((char *)m_strOffLineDataPath.c_str(),&vFileInfo))
	{
		sprintf(cErr,"__ReadEventFromDisk():在与客户端%s联通后，读取可能的离线文件时发生异常:%s。",m_pSrvParam->pClientCfg->strCliName.c_str(),m_strOffLineDataPath.c_str());
		RcdErrLogWithParentClass(cErr,"CNXEcSrvMsgOperaObj");
		return -1;
	}
	if ( vFileInfo.size() == 0 )
	{
		RcdErrLogWithParentClass("本地无缓冲离线数据需要上送。","CNXEcSrvMsgOperaObj");
		return 0;
	}
	//读取每一个文件并将内容转成消息结构，填到上送队列中。
	for (int i=0; i<vFileInfo.size(); i++ )
	{
		string strFileName = vFileInfo[i].chName;
		std::size_t ifound = strFileName.find("offline");
		if ( ifound == std::string::npos )
		{
			sprintf(cErr,"__ReadEventFromDisk():获取到的文件不属于离线储存文件，直接删除:%s.",strFileName.c_str());
			RcdErrLogWithParentClass(cErr,"CNXEcSrvMsgOperaObj");
			std::size_t ifoundPath = strFileName.find(FILE_PATH_OPT_STR);
			if ( ifoundPath == std::string::npos )
			{
				strFileName = m_strOffLineDataPath  + strFileName;
			}
			remove(strFileName.c_str());
			continue;
		}
		sprintf(cErr,"__ReadEventFromDisk():获取到离线数据保存文件,开始其中的数据:%s.",strFileName.c_str());
		RcdErrLogWithParentClass(cErr,"CNXEcSrvMsgOperaObj");
		__CvtFiletoEventMsg(strFileName);
		remove(strFileName.c_str());
		sprintf(cErr,"__ReadEventFromDisk():离线数据保存文件处理完成，删除:%s.",strFileName.c_str());
		RcdErrLogWithParentClass(cErr,"CNXEcSrvMsgOperaObj");
	}
	//删除每一个文件
	return 0;
}

/**
* @brief		将事件消息结构体格式化成字符流以便进行保存。			
* @param[in]	NX_EVENT_MESSAGE & Msg：待格式化的消息结构
* @param[in]	const char * pStream：数据流内存指针。
* @param[in]	int nLen:数据流长度
**/
void CNXEcSrvMsgOperaObj::__CvtEventMsgToStream(NX_EVENT_MESSAGE & Msg,vector<u_int8> & vStream)
{

	int p=0;

	vStream.resize(128);
	memcpy(&vStream[p],Msg.c_src_name,64);		p+=64;
	memcpy(&vStream[p],Msg.c_dst_name,64);		p+=64;
	vStream.resize(p+4);
	memcpy(&vStream[p],&Msg.n_send_utctm,4);	p+=4;
	vStream.resize(p+4);
	memcpy(&vStream[p],&Msg.n_msg_topic,4);		p+=4;
	vStream.resize(p+4);
	memcpy(&vStream[p],&Msg.n_msg_type,4);		p+=4;
	vStream.resize(p+64);
	memcpy(&vStream[p],Msg.c_invoke_id,64);	p+=64;
	vStream.resize(p+4);
	memcpy(&vStream[p],&Msg.n_event_obj,4);		p+=4;
	vStream.resize(p+4);
	memcpy(&vStream[p],&Msg.n_obj_type,4);		p+=4;
	vStream.resize(p+4);
	memcpy(&vStream[p],&Msg.n_data_src,4);		p+=4;
	vStream.resize(p+128);
	memcpy(&vStream[p],Msg.c_suffix,128);		p+=128;
	vStream.resize(p+4);
	memcpy(&vStream[p],&Msg.n_backup,4);		p+=4;
	//增加一个子项个数。
	int nSubFiledNum = Msg.list_subfields.size();
	vStream.resize(p+4);
	memcpy(&vStream[p],&nSubFiledNum,4);		p+=4;

	for ( int i=0 ; i<nSubFiledNum ; i++ )
	{	
		NX_EVENT_FIELD_STRUCT * pSubField = &Msg.list_subfields[i];
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_obj_id,4);		p+=4;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_obj_type,4);	p+=4;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_sub_obj_id,4);	p+=4;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_sub_sub_obj_id,4);	p+=4;
		vStream.resize(p+128);
		memcpy(&vStream[p],pSubField->c_field_name,128);	p+=128;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_field_type,4);	p+=4;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_value,4);		p+=4;
		vStream.resize(p+32);
		memcpy(&vStream[p],pSubField->c_value_f,32);	p+=32;
		vStream.resize(p+32);
		memcpy(&vStream[p],pSubField->c_value_s,32);	p+=32;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_quality,4);	p+=4;

		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_curvalueutctm,4);	p+=4;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_curms,4);		p+=4;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_fltstartutctm,4);	p+=4;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_fltstartms,4);		p+=4;
		vStream.resize(p+4);
		memcpy(&vStream[p],&pSubField->n_gridfan,4);	p+=4;
		
		vStream.resize(p+8);
		memcpy(&vStream[p],&pSubField->f_backup,8);		p+=8;
	}
}

/**
* @brief		将数据流写入指定的文件，写入前判断文件创建日期是否为当前日期：是-追加；否-重写。			
* @param[in]    vector<u_int8> & vStream：数据流
* @param[in]    const char * cFileName：文件名
**/
void CNXEcSrvMsgOperaObj::__WriteStream(vector<u_int8> & vStream,const char * cFileName)
{
	int nFileFlag = 0;  //0-需要全新方式打开文件。1-需要追加方式打开文件。
	char cTmp[500] = "";
	_FILE_PROPERTY_INF FileInfo;
	if ( 0 == sy_dirfile_exist(cFileName)) //文件已存在
	{
		strcpy(cTmp,cFileName);
		if ( 0 == sy_get_file_property(cTmp,&FileInfo))
		{
			CTimeConvert CCvtTimeGet(FileInfo.nLastTime);
			CTimeConvert CCvtTimeCur(time(NULL));
			MY_TIME_INFO tGet,tCur;
			CCvtTimeGet.GetTimeOfMyTime(tGet);
			CCvtTimeCur.GetTimeOfMyTime(tCur);
			if ( (tGet.nYear == tCur.nYear) && (tGet.nMon == tCur.nMon) && (tGet.nDay == tCur.nDay))
			{
				nFileFlag = 1;
			}
			else
			{
				nFileFlag = 0;
			}
			
		}
		else
		{
			nFileFlag = 0;
		}
	}
	else
	{
		nFileFlag = 0;
	}
	CFileOperate CRcdFile; 
	bool bFile = false;
	if ( nFileFlag == 0 )
	{
		bFile = CRcdFile.OpenFile(cFileName,CFileOperate::modeCreate);
	}
	else
	{
		bFile = CRcdFile.OpenFile(cFileName,CFileOperate::modeNoTruncate);;
	}

	if ( !bFile)
	{
		sprintf(cTmp,"由于客户端[%s]通信中断：收到动作事件准备往临时文件缓存时，创建文件失败：%s。",m_pSrvParam->pClientCfg->strCliName.c_str(),cFileName);
		RcdErrLogWithParentClass(cTmp,"CNXEcSrvMsgOperaObj");
		return;
	}

	int nLen = vStream.size() + 21;
	char * pChar = new char[nLen];

	memset(pChar,0,nLen);
	char cTmp1[20] = "";
	sprintf(cTmp1,"%d",vStream.size());
	memcpy(pChar,cTmp1,20);
	memcpy(pChar+20,&vStream[0],vStream.size());
	CRcdFile.WriteData(pChar,nLen-1);
	CRcdFile.CloseFile();
	delete[] pChar;
	pChar = NULL;
}


/**
* @brief		将字符流转成事件消息结构体。			
* @param[in]	NX_EVENT_MESSAGE & Msg：消息结构
* @param[in]	const char * pStream：数据流内存指针。
* @param[in]	int nLen:数据流长度
**/
bool CNXEcSrvMsgOperaObj::__CvtStreamToEventMsg(IN vector<u_int8> & vStream, OUT NX_EVENT_MESSAGE & Msg)
{
	char cErr[300] = "";
	int p=0;
	int nNeedLen = 64 + 64 + 4 + 4 + 4 + 64 + 4 + 4 + 4 + 128 + 4 + 4;
	if ( nNeedLen > vStream.size() )
	{
		RcdErrLogWithParentClass("需要解析的字符流长度不足，无法解析！","CNXEcSrvMsgOperaObj");
		return false;
	}

	memcpy(Msg.c_src_name,&vStream[p],64);		p+=64;
	memcpy(Msg.c_dst_name,&vStream[p],64);		p+=64;
	memcpy(&Msg.n_send_utctm,&vStream[p],4);	p+=4;
	memcpy(&Msg.n_msg_topic,&vStream[p],4);		p+=4;
	memcpy(&Msg.n_msg_type,&vStream[p],4);		p+=4;

	memcpy(Msg.c_invoke_id,&vStream[p],64);		p+=64;
	memcpy(&Msg.n_event_obj,&vStream[p],4);		p+=4;
	memcpy(&Msg.n_obj_type,&vStream[p],4);		p+=4;
	memcpy(&Msg.n_data_src,&vStream[p],4);		p+=4;
	memcpy(Msg.c_suffix,&vStream[p],128);		p+=128;
	memcpy(&Msg.n_backup,&vStream[p],4);		p+=4;
	//获取子项个数
	int nSubFiledNum = 0;
	memcpy(&nSubFiledNum,&vStream[p],4);		p+=4;

	nNeedLen = 4*4+128+4*2+32*2+4*6+8;
	if ( (nNeedLen * nSubFiledNum) > (vStream.size() - p))
	{
		RcdErrLogWithParentClass("需要解析的字符流长度不足，无法解析剩余子项！","CNXEcSrvMsgOperaObj");
		return false;
	}
	for ( int i=0 ; i<nSubFiledNum ; i++ )
	{	
		NX_EVENT_FIELD_STRUCT SubField ;
		memcpy(&SubField.n_obj_id,&vStream[p],4);		p+=4;
		memcpy(&SubField.n_obj_type,&vStream[p],4);		p+=4;
		memcpy(&SubField.n_sub_obj_id,&vStream[p],4);	p+=4;
		memcpy(&SubField.n_sub_sub_obj_id,&vStream[p],4);	p+=4;
		memcpy(SubField.c_field_name,&vStream[p],128);	p+=128;
		memcpy(&SubField.n_field_type,&vStream[p],4);	p+=4;
		memcpy(&SubField.n_value,&vStream[p],4);		p+=4;
		memcpy(SubField.c_value_f,&vStream[p],32);		p+=32;
		memcpy(SubField.c_value_s,&vStream[p],32);		p+=32;
		memcpy(&SubField.n_quality,&vStream[p],4);		p+=4;

		memcpy(&SubField.n_curvalueutctm,&vStream[p],4);p+=4;
		memcpy(&SubField.n_curms,&vStream[p],4);		p+=4;
		memcpy(&SubField.n_fltstartutctm,&vStream[p],4);p+=4;
		memcpy(&SubField.n_fltstartms,&vStream[p],4);	p+=4;
		memcpy(&SubField.n_gridfan,&vStream[p],4);		p+=4;
		memcpy(&SubField.f_backup,&vStream[p],8);		p+=8;

		Msg.list_subfields.push_back(SubField);
	}
	return true;
}

/**
* @brief		将文件内容转成事件结构体。			
* @param[in]    strFileName：文件
**/
void CNXEcSrvMsgOperaObj::__CvtFiletoEventMsg(string & strFileName)
{
	char cErr[255] = "";

	FILE * pFile = fopen(strFileName.c_str(),"r+b");
	if ( NULL == pFile )
	{
		sprintf(cErr,"__CvtFiletoEventMsg():打开文件失败-%s.",strFileName.c_str());
		RcdErrLogWithParentClass(cErr,"CNXEcSrvMsgOperaObj");
		return;
	}
	while (!feof(pFile))
	{
		char cLen[21]="";
		int nRead = fread(cLen,sizeof(char),20,pFile);
		int nLen = atoi(cLen);
		if ( nLen == 0 )
		{
			RcdErrLogWithParentClass("__CvtFiletoEventMsg():读取到文件内容长度为0.","CNXEcSrvMsgOperaObj");
			fclose(pFile);
			return;
		}
		char * pChar = new char[nLen];
		nRead = fread(pChar,sizeof(char),nLen,pFile);
		if ( nRead != nLen )
		{
			sprintf(cErr,"__CvtFiletoEventMsg():读取文件内容有误（指定读取%d字节，实际读到%d字节）.",nLen,nRead);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvMsgOperaObj");
			fclose(pFile);
			return;
		}
		NX_EVENT_MESSAGE  EventMsg;
		vector<u_int8> vStream;
		vStream.resize(nLen);
		memcpy(&vStream[0],pChar,nLen);
		RcdTrcLogWithParentClass("开始转换字符串到消息结构","CNXEcSrvMsgOperaObj");
		if (__CvtStreamToEventMsg(vStream,EventMsg))
		{
			m_EventDeque.push_back(EventMsg);
			RcdTrcLogWithParentClass("转换成功","CNXEcSrvMsgOperaObj");
		}
		else
		{
			RcdTrcLogWithParentClass("转换失败","CNXEcSrvMsgOperaObj");
		}

		delete[] pChar;
		pChar = NULL;
	}
	fclose(pFile);
}

/**
* @brief		获取主站离线第几天。			
* @param[in]    nDay：当前的日期
* @return		应该的编号
* @note 
**/
int CNXEcSrvMsgOperaObj::__GetFileSerialNo()
{
	time_t tCurrent = time(NULL);
	int nSecOfOneDay = 60*60*24;
	int nBeginDay = m_tFirstOffLineEvent/nSecOfOneDay;
	int nCurrDay = tCurrent/nSecOfOneDay;//转成天。
	int nDayLen = 10 ;
	int nValue = (nCurrDay - nBeginDay) > 0 ? (nCurrDay - nBeginDay):0;
	return (nValue % nDayLen);
}

/**
* @brief	    根据当前通道查询所属客户端下所有通道的通信状态。如果有一个通道为为通，则返回客户端的通信状态为通。			
* @param[out]    bMyChlIdIsBiggest:本通道编号是否为最大的一个。只有编号最大的通道才去再客户端断开的情况下写缓存文件
* @return		0-客户端为断开；-1-查询失败；1-客户端为通。
* @note 
**/
int CNXEcSrvMsgOperaObj::__GetClientStatus(bool & bMyChlIdIsBiggest)
{
	//获取数据操作对象指针
	if( m_pModelSeekIns->GetModelMgrObj() == NULL )
		return -1;
	 INXEcModelMgr * pModelMgr =  m_pModelSeekIns->GetModelMgrObj();
	 //查询本通道所属客户端下的所有通道
	 int nCliId = m_pSrvParam->pClientCfg->n_cli_id;

	 char cError[512]="";
	 
	 DB_OPER_PARAM SelectParam;       // 数据库查询参数
	 CPlmRecordSet SelectResultSet;   // 数据库查询结果集
	 char cValue[100] = "";

	 // 设置查询表名称
	 SelectParam.lst_tablename.push_back("nx_t_ecu_channel_cfg");
	 // 设置查询字段
	 _add_db_field("obj_id",FD_TYPE_NUMERIC,SelectParam.lst_fddata);
	 _add_db_field("aliasname",FD_TYPE_CHAR,SelectParam.lst_fddata);
	 _add_db_field("ipaddr",FD_TYPE_CHAR,SelectParam.lst_fddata);
	 _add_db_field("cmmustat",FD_TYPE_NUMERIC,SelectParam.lst_fddata);


	 // 设置查询条件
	 // 条件
	 sprintf(cValue,"%d",nCliId);
	 _add_db_condition("client_obj",CDT_TYPE_EQUAL,cValue,FD_TYPE_NUMERIC,CDT_LOGIAL_AND,SelectParam.lst_condition);

	 // 从数据库获取信息
	 char cDes[255]="";
	 if( pModelMgr->dbm_select_records(&SelectParam,SelectResultSet,false,cDes) != 0)
	 {
		 _clear_db_opera_param(SelectParam);
		 sprintf(cError,"__GetClientStatus():获取客户端ID=%d下所有通道状态失败，原因为:%s",nCliId);
		 RcdErrLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		 return -1;
	 }

	 _clear_db_opera_param(SelectParam);

	 // 根据查询结果生成开关量召唤结果结构
	 UINT nRecordNum = 0;
	 SelectResultSet.get_record_num(nRecordNum);
	 int nCliStatus = 0;

	 for (int i = 0; i< nRecordNum; i++ )
	 {
		 // 获得指定字段的值
		 string strChlId; strChlId.clear();
		 string strChlName;strChlId.clear();
		 string strChlIp;strChlId.clear();
		 string strChlStatus;strChlId.clear();
		 SelectResultSet.get_field_value(1,strChlId); 
		 SelectResultSet.get_field_value(2,strChlName);
		 SelectResultSet.get_field_value(3,strChlIp);
		 SelectResultSet.get_field_value(4,strChlStatus);
		 //2024/8/7 现场发现之前送过的事件信息，在主站断开重连后又送了一遍
		 if (atoi(strChlStatus.c_str()) == 1)
		 {
			 nCliStatus = 1;
		 }

		 //nCliStatus = atoi(strChlStatus.c_str()) == 1? 1:0;
// 		 sprintf(cError,"__GetClientStatus():获取客户端ID=%d下通道[id=%s,name=%s,ip=%s]状态值为[%s]",
// 						nCliId,strChlId.c_str(),strChlName.c_str(),strChlIp.c_str(),strChlStatus.c_str());
// 		 RcdTrcLogWithParentClass(cError,"CNXEcSrvMsgOperaObj");
		 //判断通达是不是还有最大值
		 if ( (m_pSrvParam->pClientCfg->n_channel_id) < (atoi(strChlId.c_str())) )
		 {
			 bMyChlIdIsBiggest = false;
		 }
		 // 下一条
		 SelectResultSet.move_to_next();
	 }

	 //
	 return nCliStatus;
	 
}
 