﻿生成启动时间为 2025/7/29 16:40:33。
     1>项目“D:\code\pro\nx_ec_pro_commuflow_104\nx_ec_pro_commuflow_104.vcxproj”在节点 4 上(build 个目标)。
     1>C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\Microsoft.CppBuild.targets(299,5): warning MSB8004: Output 目录未以斜杠结尾。此生成实例将添加斜杠，因为必须有这个斜杠才能正确计算 Output 目录。
     1>InitializeBuildStatus:
         正在创建“Debug\nx_ec_pro_commuflow_104.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         D:\vs2010\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDLL /D _MBCS /D __PLATFORM_MS_WIN__ /D _CRT_SECURE_NO_WARNINGS /D NX_EC_PRO_TRANS_EXPORT /D _USE_32BIT_TIME_T /Gm /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Fo"Debug\\" /Fd"Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ..\..\..\..\..\nx_common\CsgLogRecord.cpp ..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp ..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp ..\..\ec_common\EcTemplateFunc.cpp ..\..\ec_common\NXECObject.cpp ..\ec_pro_common\NXEcProTransObj.cpp ..\ec_pro_common_iec60870\NXEcIec104ProExplain.cpp ec_104flow_modify_note.cpp ec_pro_commuflow_104_export.cpp NXEc104ProCliTransObj.cpp NXEc104ProSrvTransObj.cpp NXEc104ProTransObj.cpp NXEc104RSNMgr.cpp
         NXEc104RSNMgr.cpp
     1>d:\code\pro\nx_ec_pro_commuflow_104\nxec104rsnmgr.h(11): fatal error C1083: 无法打开包括文件:“ThreadMutualLock.h”: No such file or directory
         NXEc104ProTransObj.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEc104ProSrvTransObj.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEc104ProCliTransObj.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         ec_pro_commuflow_104_export.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         ec_104flow_modify_note.cpp
         NXEcIec104ProExplain.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProTransObj.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXECObject.cpp
     1>d:\code\ec_common\nxecobject.h(11): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         EcTemplateFunc.cpp
     1>d:\code\ec_common\ectemplatefunc.h(11): fatal error C1083: 无法打开包括文件:“inxmb_def.h”: No such file or directory
         SYCommunDllPack.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp”: No such file or directory
         CsgLogRecordMngr.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp”: No such file or directory
         CsgLogRecord.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\nx_common\CsgLogRecord.cpp”: No such file or directory
         正在生成代码...
     1>已完成生成项目“D:\code\pro\nx_ec_pro_commuflow_104\nx_ec_pro_commuflow_104.vcxproj”(build 个目标)的操作 - 失败。

生成失败。

已用时间 00:00:01.92
