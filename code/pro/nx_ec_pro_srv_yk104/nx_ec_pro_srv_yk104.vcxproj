﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7ACF8B99-BA0E-4297-9699-EE206E85981D}</ProjectGuid>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <Keyword>ManagedCProj</Keyword>
    <RootNamespace>nx_ec_pro_srv_yk104</RootNamespace>
    <ProjectName>nx_ec_pro_srv_yk104</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/</IncludePath>
    <LibraryPath>..\..\..\..\..\nx_lib\debug;$(LibraryPath)</LibraryPath>
    <OutDir>../../../../../nx_bin/debug/nx_ec</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/;</IncludePath>
    <LibraryPath>..\..\..\..\..\nx_lib\release;$(LibraryPath)</LibraryPath>
    <OutDir>../../../../../nx_bin/release/nx_ec</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_USE_32BIT_TIME_T;_DEBUG;__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_PROTOCOL_EXPORT</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;_USE_32BIT_TIME_T;NDEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_PROTOCOL_EXPORT;</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_mb.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\nx_common\nx_errno_def.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\FileOperate.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\LogRecord.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\MyDeque.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_def.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_fun.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\plm_dbm\DataRow.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\ThreadMutualLock.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\TimeConvert.h" />
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h" />
    <ClInclude Include="..\..\ec_common\NXECObject.h" />
    <ClInclude Include="..\ec_pro_common\EcProCommonFun.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoad60870FlowLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoadMsgOperationLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoadProOperationLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcProtocol.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu10.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu21.h" />
    <ClInclude Include="NXEc60870CvtFactory_YK.h" />
    <ClInclude Include="NXEc60870CvtObj_YK.h" />
    <ClInclude Include="NXEcIec104ExplainFactory_YK.h" />
    <ClInclude Include="NXEcIec104ProExplain_YK.h" />
    <ClInclude Include="NXEcProAsdu167_YK.h" />
    <ClInclude Include="NXEcProAsdu1_YK.h" />
    <ClInclude Include="NXEcProAsdu31_YK.h" />
    <ClInclude Include="NXEcProAsdu34_YK.h" />
    <ClInclude Include="NXEcProAsdu45_YK.h" />
    <ClInclude Include="NXEcProAsdu49_YK.h" />
    <ClInclude Include="NXEcProAsdu100_YK.h" />
    <ClInclude Include="NXEcProAsdu_YK.h" />
    <ClInclude Include="NXEcYK104SrvProtocol.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp" />
    <ClCompile Include="..\..\ec_common\NXECObject.cpp" />
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp" />
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp" />
    <ClCompile Include="ec_pro_srv_yk104_export.cpp" />
    <ClCompile Include="NXEc60870CvtFactory_YK.cpp" />
    <ClCompile Include="NXEc60870CvtObj_YK.cpp" />
    <ClCompile Include="NXEcIec104ExplainFactory_YK.cpp" />
    <ClCompile Include="NXEcIec104ProExplain_YK.cpp" />
    <ClCompile Include="NXEcProAsdu167_YK.cpp" />
    <ClCompile Include="NXEcProAsdu1_YK.cpp" />
    <ClCompile Include="NXEcProAsdu31_YK.cpp" />
    <ClCompile Include="NXEcProAsdu34_YK.cpp" />
    <ClCompile Include="NXEcProAsdu45_YK.cpp" />
    <ClCompile Include="NXEcProAsdu49_YK.cpp" />
    <ClCompile Include="NXEcProAsdu100_YK.cpp" />
    <ClCompile Include="NXEcProAsdu_YK.cpp" />
    <ClCompile Include="NXEcYK104SrvProtocol.cpp" />
    <ClCompile Include="nx_ec_pro_srv_yk104_modify_note.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_srv_yk104.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>